import { getUserInfo, logout as keycloakLogout } from '@/utils/keycloak'
import { request } from '@/utils/request'

const state = {
  userInfo: null,
  profile: null,
  permissions: [],
  activities: []
}

const mutations = {
  SET_USER_INFO(state, userInfo) {
    state.userInfo = userInfo
  },
  
  SET_PROFILE(state, profile) {
    state.profile = profile
  },
  
  SET_PERMISSIONS(state, permissions) {
    state.permissions = permissions
  },
  
  SET_ACTIVITIES(state, activities) {
    state.activities = activities
  },
  
  CLEAR_USER_DATA(state) {
    state.userInfo = null
    state.profile = null
    state.permissions = []
    state.activities = []
  }
}

const actions = {
  // 获取用户信息
  async fetchUserInfo({ commit }) {
    try {
      // 从 Keycloak 获取基本信息
      const keycloakUserInfo = getUserInfo()
      commit('SET_USER_INFO', keycloakUserInfo)
      
      // 从后端获取详细信息
      const profile = await request.get('/auth/profile/')
      commit('SET_PROFILE', profile)
      
      return { keycloakUserInfo, profile }
    } catch (error) {
      console.error('Failed to fetch user info:', error)
      throw error
    }
  },
  
  // 获取用户活动记录
  async fetchUserActivities({ commit }) {
    try {
      const activities = await request.get('/core/activities/')
      commit('SET_ACTIVITIES', activities)
      return activities
    } catch (error) {
      console.error('Failed to fetch user activities:', error)
      throw error
    }
  },
  
  // 更新用户资料
  async updateProfile({ commit }, profileData) {
    try {
      const updatedProfile = await request.put('/auth/profile/', profileData)
      commit('SET_PROFILE', updatedProfile)
      return updatedProfile
    } catch (error) {
      console.error('Failed to update profile:', error)
      throw error
    }
  },
  
  // 登出
  async logout({ commit }) {
    try {
      // 调用后端登出接口
      await request.post('/auth/logout/')
    } catch (error) {
      console.error('Backend logout error:', error)
    } finally {
      // 清除本地状态
      commit('CLEAR_USER_DATA')
      
      // Keycloak 登出
      keycloakLogout()
    }
  }
}

const getters = {
  isLoggedIn: state => !!state.userInfo,
  
  username: state => state.userInfo?.username || '',
  
  email: state => state.userInfo?.email || '',
  
  fullName: state => state.profile?.full_name || state.userInfo?.name || '',
  
  roles: state => state.userInfo?.roles || [],
  
  hasRole: state => role => {
    return state.userInfo?.roles?.includes(role) || false
  },
  
  recentActivities: state => state.activities.slice(0, 10)
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}

import { request } from '@/utils/request'

const state = {
  // 测试项目
  projects: [],
  currentProject: null,
  projectStats: {},
  
  // 测试数据
  modalData: [],
  airtightnessData: [],
  
  // 车辆和零部件
  vehicles: [],
  components: [],
  
  // 分页信息
  pagination: {
    page: 1,
    pageSize: 20,
    total: 0
  }
}

const mutations = {
  SET_PROJECTS(state, projects) {
    state.projects = projects
  },
  
  SET_CURRENT_PROJECT(state, project) {
    state.currentProject = project
  },
  
  SET_PROJECT_STATS(state, stats) {
    state.projectStats = stats
  },
  
  SET_MODAL_DATA(state, data) {
    state.modalData = data
  },
  
  SET_AIRTIGHTNESS_DATA(state, data) {
    state.airtightnessData = data
  },
  
  SET_VEHICLES(state, vehicles) {
    state.vehicles = vehicles
  },
  
  SET_COMPONENTS(state, components) {
    state.components = components
  },
  
  SET_PAGINATION(state, pagination) {
    state.pagination = { ...state.pagination, ...pagination }
  },
  
  ADD_PROJECT(state, project) {
    state.projects.unshift(project)
  },
  
  UPDATE_PROJECT(state, updatedProject) {
    const index = state.projects.findIndex(p => p.id === updatedProject.id)
    if (index !== -1) {
      state.projects.splice(index, 1, updatedProject)
    }
  },
  
  REMOVE_PROJECT(state, projectId) {
    state.projects = state.projects.filter(p => p.id !== projectId)
  }
}

const actions = {
  // 获取测试项目列表
  async fetchProjects({ commit }, params = {}) {
    try {
      const response = await request.get('/testing/projects/', params)
      commit('SET_PROJECTS', response.results || response)
      
      if (response.count !== undefined) {
        commit('SET_PAGINATION', {
          total: response.count,
          page: params.page || 1
        })
      }
      
      return response
    } catch (error) {
      console.error('Failed to fetch projects:', error)
      throw error
    }
  },
  
  // 获取项目统计信息
  async fetchProjectStats({ commit }) {
    try {
      const stats = await request.get('/testing/projects/statistics/')
      commit('SET_PROJECT_STATS', stats)
      return stats
    } catch (error) {
      console.error('Failed to fetch project stats:', error)
      throw error
    }
  },
  
  // 获取项目详情
  async fetchProject({ commit }, projectId) {
    try {
      const project = await request.get(`/testing/projects/${projectId}/`)
      commit('SET_CURRENT_PROJECT', project)
      return project
    } catch (error) {
      console.error('Failed to fetch project:', error)
      throw error
    }
  },
  
  // 创建项目
  async createProject({ commit }, projectData) {
    try {
      const project = await request.post('/testing/projects/', projectData)
      commit('ADD_PROJECT', project)
      return project
    } catch (error) {
      console.error('Failed to create project:', error)
      throw error
    }
  },
  
  // 更新项目
  async updateProject({ commit }, { id, data }) {
    try {
      const project = await request.put(`/testing/projects/${id}/`, data)
      commit('UPDATE_PROJECT', project)
      return project
    } catch (error) {
      console.error('Failed to update project:', error)
      throw error
    }
  },
  
  // 删除项目
  async deleteProject({ commit }, projectId) {
    try {
      await request.delete(`/testing/projects/${projectId}/`)
      commit('REMOVE_PROJECT', projectId)
    } catch (error) {
      console.error('Failed to delete project:', error)
      throw error
    }
  },
  
  // 获取模态测试数据
  async fetchModalData({ commit }, params = {}) {
    try {
      const response = await request.get('/testing/modal-data/', params)
      commit('SET_MODAL_DATA', response.results || response)
      return response
    } catch (error) {
      console.error('Failed to fetch modal data:', error)
      throw error
    }
  },
  
  // 获取气密性测试数据
  async fetchAirtightnessData({ commit }, params = {}) {
    try {
      const response = await request.get('/testing/airtightness-data/', params)
      commit('SET_AIRTIGHTNESS_DATA', response.results || response)
      return response
    } catch (error) {
      console.error('Failed to fetch airtightness data:', error)
      throw error
    }
  },
  
  // 获取车辆列表
  async fetchVehicles({ commit }, params = {}) {
    try {
      const response = await request.get('/testing/vehicles/', params)
      commit('SET_VEHICLES', response.results || response)
      return response
    } catch (error) {
      console.error('Failed to fetch vehicles:', error)
      throw error
    }
  },
  
  // 获取零部件列表
  async fetchComponents({ commit }, params = {}) {
    try {
      const response = await request.get('/testing/components/', params)
      commit('SET_COMPONENTS', response.results || response)
      return response
    } catch (error) {
      console.error('Failed to fetch components:', error)
      throw error
    }
  }
}

const getters = {
  projectsByType: state => type => {
    return state.projects.filter(project => project.project_type === type)
  },
  
  projectsByStatus: state => status => {
    return state.projects.filter(project => project.status === status)
  },
  
  activeProjects: state => {
    return state.projects.filter(project => project.status === 'active')
  },
  
  completedProjects: state => {
    return state.projects.filter(project => project.status === 'completed')
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}

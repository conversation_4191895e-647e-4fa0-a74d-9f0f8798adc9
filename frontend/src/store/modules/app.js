const state = {
  // 侧边栏状态
  sidebar: {
    opened: localStorage.getItem('sidebarStatus') ? !!+localStorage.getItem('sidebarStatus') : true,
    withoutAnimation: false
  },
  
  // 设备类型
  device: 'desktop',
  
  // 系统配置
  systemConfig: {},
  
  // 加载状态
  loading: false,
  
  // 面包屑导航
  breadcrumbs: []
}

const mutations = {
  TOGGLE_SIDEBAR(state) {
    state.sidebar.opened = !state.sidebar.opened
    state.sidebar.withoutAnimation = false
    if (state.sidebar.opened) {
      localStorage.setItem('sidebarStatus', 1)
    } else {
      localStorage.setItem('sidebarStatus', 0)
    }
  },
  
  CLOSE_SIDEBAR(state, withoutAnimation) {
    localStorage.setItem('sidebarStatus', 0)
    state.sidebar.opened = false
    state.sidebar.withoutAnimation = withoutAnimation
  },
  
  TOGGLE_DEVICE(state, device) {
    state.device = device
  },
  
  SET_SYSTEM_CONFIG(state, config) {
    state.systemConfig = config
  },
  
  SET_LOADING(state, loading) {
    state.loading = loading
  },
  
  SET_BREADCRUMBS(state, breadcrumbs) {
    state.breadcrumbs = breadcrumbs
  }
}

const actions = {
  toggleSideBar({ commit }) {
    commit('TOGGLE_SIDEBAR')
  },
  
  closeSideBar({ commit }, { withoutAnimation }) {
    commit('CLOSE_SIDEBAR', withoutAnimation)
  },
  
  toggleDevice({ commit }, device) {
    commit('TOGGLE_DEVICE', device)
  },
  
  async fetchSystemConfig({ commit }) {
    try {
      const { request } = await import('@/utils/request')
      const config = await request.get('/core/config/')
      commit('SET_SYSTEM_CONFIG', config)
      return config
    } catch (error) {
      console.error('Failed to fetch system config:', error)
      throw error
    }
  },
  
  setLoading({ commit }, loading) {
    commit('SET_LOADING', loading)
  },
  
  setBreadcrumbs({ commit }, breadcrumbs) {
    commit('SET_BREADCRUMBS', breadcrumbs)
  }
}

const getters = {
  sidebar: state => state.sidebar,
  device: state => state.device,
  systemConfig: state => state.systemConfig,
  loading: state => state.loading,
  breadcrumbs: state => state.breadcrumbs
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}

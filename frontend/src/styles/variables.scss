// 基础颜色变量
$blue: #409EFF;
$green: #67C23A;
$red: #F56C6C;
$yellow: #E6A23C;
$orange: #FF9500;
$purple: #9C27B0;

// 中性色
$white: #FFFFFF;
$black: #000000;
$gray-1: #F5F7FA;
$gray-2: #EBEEF5;
$gray-3: #E4E7ED;
$gray-4: #DCDFE6;
$gray-5: #C0C4CC;
$gray-6: #909399;
$gray-7: #606266;
$gray-8: #303133;

// 主题色
$primary-color: $blue;
$success-color: $green;
$warning-color: $yellow;
$danger-color: $red;
$info-color: $gray-6;

// 文字颜色
$text-color-primary: $gray-8;
$text-color-regular: $gray-7;
$text-color-secondary: $gray-6;
$text-color-placeholder: $gray-5;

// 边框颜色
$border-color-base: $gray-4;
$border-color-light: $gray-3;
$border-color-lighter: $gray-2;
$border-color-extra-light: $gray-1;

// 背景色
$background-color-base: $gray-1;

// 侧边栏
$menuText: #bfcbd9;
$menuActiveText: #409EFF;
$subMenuActiveText: #f4f4f5;

$menuBg: #304156;
$menuHover: #263445;

$subMenuBg: #1f2d3d;
$subMenuHover: #001528;

$sideBarWidth: 210px;

// 导出变量供 JavaScript 使用
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
}

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑项目' : '新建项目'"
    width="600px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="项目名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入项目名称" />
      </el-form-item>
      
      <el-form-item label="项目类型" prop="project_type">
        <el-select v-model="form.project_type" placeholder="请选择项目类型">
          <el-option
            v-for="type in projectTypes"
            :key="type.value"
            :label="type.label"
            :value="type.value"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="项目状态" prop="status">
        <el-select v-model="form.status" placeholder="请选择项目状态">
          <el-option
            v-for="status in projectStatuses"
            :key="status.value"
            :label="status.label"
            :value="status.value"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="车型" prop="vehicle_model">
        <el-input v-model="form.vehicle_model" placeholder="请输入车型" />
      </el-form-item>
      
      <el-form-item label="测试地点" prop="test_location">
        <el-input v-model="form.test_location" placeholder="请输入测试地点" />
      </el-form-item>
      
      <el-form-item label="开始日期" prop="start_date">
        <el-date-picker
          v-model="form.start_date"
          type="date"
          placeholder="选择开始日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      
      <el-form-item label="结束日期" prop="end_date">
        <el-date-picker
          v-model="form.end_date"
          type="date"
          placeholder="选择结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      
      <el-form-item label="项目描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入项目描述"
        />
      </el-form-item>
      
      <el-form-item label="备注" prop="notes">
        <el-input
          v-model="form.notes"
          type="textarea"
          :rows="2"
          placeholder="请输入备注信息"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { useStore } from 'vuex'
import { ElMessage } from 'element-plus'

export default {
  name: 'ProjectFormDialog',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    project: {
      type: Object,
      default: null
    }
  },
  emits: ['update:modelValue', 'success'],
  setup(props, { emit }) {
    const store = useStore()
    const formRef = ref(null)
    const loading = ref(false)
    
    const dialogVisible = computed({
      get: () => props.modelValue,
      set: (value) => emit('update:modelValue', value)
    })
    
    const isEdit = computed(() => !!props.project)
    
    const form = reactive({
      name: '',
      project_type: '',
      status: 'draft',
      vehicle_model: '',
      test_location: '',
      start_date: '',
      end_date: '',
      description: '',
      notes: ''
    })
    
    const rules = {
      name: [
        { required: true, message: '请输入项目名称', trigger: 'blur' }
      ],
      project_type: [
        { required: true, message: '请选择项目类型', trigger: 'change' }
      ],
      status: [
        { required: true, message: '请选择项目状态', trigger: 'change' }
      ]
    }
    
    const projectTypes = [
      { value: 'modal', label: '模态测试' },
      { value: 'airtightness', label: '气密性测试' },
      { value: 'sound_insulation', label: '隔音测试' },
      { value: 'sound_absorption', label: '吸音测试' },
      { value: 'sound_transmission', label: '传声测试' },
      { value: 'wall_mounted_transmission', label: '壁挂传声测试' },
      { value: 'material_porosity', label: '材料孔隙率测试' }
    ]
    
    const projectStatuses = [
      { value: 'draft', label: '草稿' },
      { value: 'active', label: '进行中' },
      { value: 'completed', label: '已完成' },
      { value: 'archived', label: '已归档' }
    ]
    
    const resetForm = () => {
      Object.keys(form).forEach(key => {
        if (key === 'status') {
          form[key] = 'draft'
        } else {
          form[key] = ''
        }
      })
      
      if (formRef.value) {
        formRef.value.clearValidate()
      }
    }
    
    const fillForm = (project) => {
      if (project) {
        Object.keys(form).forEach(key => {
          if (project[key] !== undefined) {
            form[key] = project[key]
          }
        })
      }
    }
    
    const handleSubmit = async () => {
      if (!formRef.value) return
      
      try {
        await formRef.value.validate()
        loading.value = true
        
        if (isEdit.value) {
          await store.dispatch('testing/updateProject', {
            id: props.project.id,
            data: { ...form }
          })
          ElMessage.success('项目更新成功')
        } else {
          await store.dispatch('testing/createProject', { ...form })
          ElMessage.success('项目创建成功')
        }
        
        emit('success')
        handleClose()
      } catch (error) {
        console.error('Form submission error:', error)
        ElMessage.error(isEdit.value ? '项目更新失败' : '项目创建失败')
      } finally {
        loading.value = false
      }
    }
    
    const handleClose = () => {
      dialogVisible.value = false
      resetForm()
    }
    
    // 监听对话框打开，填充表单数据
    watch(() => props.modelValue, (visible) => {
      if (visible) {
        nextTick(() => {
          if (isEdit.value) {
            fillForm(props.project)
          } else {
            resetForm()
          }
        })
      }
    })
    
    return {
      formRef,
      loading,
      dialogVisible,
      isEdit,
      form,
      rules,
      projectTypes,
      projectStatuses,
      handleSubmit,
      handleClose
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  .el-button + .el-button {
    margin-left: 12px;
  }
}
</style>

<template>
  <div ref="chartRef" class="chart"></div>
</template>

<script>
import { ref, onMounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

export default {
  name: 'ProjectTypeChart',
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  setup(props) {
    const chartRef = ref(null)
    let chartInstance = null
    
    const projectTypeMap = {
      'modal': '模态测试',
      'airtightness': '气密性测试',
      'sound_insulation': '隔音测试',
      'sound_absorption': '吸音测试',
      'sound_transmission': '传声测试',
      'wall_mounted_transmission': '壁挂传声测试',
      'material_porosity': '材料孔隙率测试'
    }
    
    const initChart = () => {
      if (!chartRef.value) return
      
      chartInstance = echarts.init(chartRef.value)
      updateChart()
    }
    
    const updateChart = () => {
      if (!chartInstance) return
      
      const chartData = props.data.map(item => ({
        name: projectTypeMap[item.project_type] || item.project_type,
        value: item.count
      }))
      
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          textStyle: {
            fontSize: 12
          }
        },
        series: [
          {
            name: '项目类型',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['60%', '50%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: chartData,
            color: [
              '#5470c6',
              '#91cc75',
              '#fac858',
              '#ee6666',
              '#73c0de',
              '#3ba272',
              '#fc8452'
            ]
          }
        ]
      }
      
      chartInstance.setOption(option)
    }
    
    const resizeChart = () => {
      if (chartInstance) {
        chartInstance.resize()
      }
    }
    
    onMounted(() => {
      nextTick(() => {
        initChart()
        window.addEventListener('resize', resizeChart)
      })
    })
    
    watch(() => props.data, () => {
      updateChart()
    }, { deep: true })
    
    return {
      chartRef
    }
  },
  beforeUnmount() {
    if (this.chartInstance) {
      this.chartInstance.dispose()
    }
    window.removeEventListener('resize', this.resizeChart)
  }
}
</script>

<style scoped>
.chart {
  width: 100%;
  height: 100%;
}
</style>

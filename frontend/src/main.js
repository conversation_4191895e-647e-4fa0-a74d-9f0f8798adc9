import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { initKeycloak } from './utils/keycloak'

// Initialize Keycloak
initKeycloak().then((keycloak) => {
  const app = createApp(App)
  
  // Register Element Plus icons
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
  }
  
  // Make keycloak available globally
  app.config.globalProperties.$keycloak = keycloak
  app.provide('keycloak', keycloak)
  
  app.use(ElementPlus)
  app.use(router)
  app.mount('#app')
}).catch(error => {
  console.error('Failed to initialize Keycloak:', error)
})

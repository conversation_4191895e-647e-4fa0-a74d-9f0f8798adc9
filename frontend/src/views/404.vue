<template>
  <div class="error-page">
    <div class="error-content">
      <div class="error-image">
        <el-icon size="120" color="#409EFF">
          <Warning />
        </el-icon>
      </div>
      
      <div class="error-info">
        <h1>404</h1>
        <h2>页面不存在</h2>
        <p>抱歉，您访问的页面不存在或已被删除</p>
        
        <div class="error-actions">
          <el-button type="primary" @click="goHome">
            <el-icon><House /></el-icon>
            返回首页
          </el-button>
          <el-button @click="goBack">
            <el-icon><Back /></el-icon>
            返回上页
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { useRouter } from 'vue-router'

export default {
  name: 'NotFound',
  setup() {
    const router = useRouter()
    
    const goHome = () => {
      router.push('/')
    }
    
    const goBack = () => {
      router.go(-1)
    }
    
    return {
      goHome,
      goBack
    }
  }
}
</script>

<style lang="scss" scoped>
.error-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
}

.error-content {
  text-align: center;
  max-width: 500px;
  padding: 40px;
  
  .error-image {
    margin-bottom: 32px;
  }
  
  .error-info {
    h1 {
      font-size: 72px;
      color: #409EFF;
      margin: 0 0 16px 0;
      font-weight: bold;
    }
    
    h2 {
      font-size: 24px;
      color: #303133;
      margin: 0 0 16px 0;
    }
    
    p {
      color: #606266;
      font-size: 16px;
      margin: 0 0 32px 0;
      line-height: 1.5;
    }
    
    .error-actions {
      .el-button + .el-button {
        margin-left: 16px;
      }
    }
  }
}

@media (max-width: 480px) {
  .error-content {
    padding: 20px;
    
    .error-info {
      h1 {
        font-size: 48px;
      }
      
      h2 {
        font-size: 20px;
      }
      
      .error-actions {
        .el-button {
          display: block;
          width: 100%;
          margin: 0 0 12px 0;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}
</style>

<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <h1>NVH 测试数据管理系统</h1>
        <p>请使用您的账户登录系统</p>
      </div>
      
      <div class="login-content">
        <el-button 
          type="primary" 
          size="large" 
          :loading="loading"
          @click="handleLogin"
          class="login-button"
        >
          <el-icon v-if="!loading"><User /></el-icon>
          {{ loading ? '登录中...' : '使用 Keycloak 登录' }}
        </el-button>
        
        <div class="login-info">
          <p>系统采用统一身份认证，请使用您的企业账户登录</p>
        </div>
      </div>
      
      <div class="login-footer">
        <p>&copy; 2024 NVH 测试数据管理系统</p>
      </div>
    </div>
    
    <!-- 背景装饰 -->
    <div class="bg-decoration">
      <div class="bg-circle circle-1"></div>
      <div class="bg-circle circle-2"></div>
      <div class="bg-circle circle-3"></div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { initKeycloak } from '@/utils/keycloak'
import { ElMessage } from 'element-plus'

export default {
  name: 'Login',
  setup() {
    const router = useRouter()
    const loading = ref(false)
    
    const handleLogin = async () => {
      loading.value = true
      
      try {
        await initKeycloak()
        ElMessage.success('登录成功')
        router.push('/')
      } catch (error) {
        console.error('Login failed:', error)
        ElMessage.error('登录失败，请重试')
      } finally {
        loading.value = false
      }
    }
    
    return {
      loading,
      handleLogin
    }
  }
}
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.login-box {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 40px;
  width: 400px;
  max-width: 90vw;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  text-align: center;
  position: relative;
  z-index: 10;
}

.login-header {
  margin-bottom: 32px;
  
  h1 {
    font-size: 28px;
    color: #303133;
    margin: 0 0 8px 0;
    font-weight: 600;
  }
  
  p {
    color: #606266;
    margin: 0;
    font-size: 14px;
  }
}

.login-content {
  margin-bottom: 32px;
  
  .login-button {
    width: 100%;
    height: 48px;
    font-size: 16px;
    border-radius: 8px;
    margin-bottom: 20px;
  }
  
  .login-info {
    p {
      color: #909399;
      font-size: 12px;
      margin: 0;
      line-height: 1.5;
    }
  }
}

.login-footer {
  p {
    color: #c0c4cc;
    font-size: 12px;
    margin: 0;
  }
}

.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  
  .bg-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
    
    &.circle-1 {
      width: 200px;
      height: 200px;
      top: 10%;
      left: 10%;
      animation-delay: 0s;
    }
    
    &.circle-2 {
      width: 150px;
      height: 150px;
      top: 60%;
      right: 10%;
      animation-delay: 2s;
    }
    
    &.circle-3 {
      width: 100px;
      height: 100px;
      bottom: 20%;
      left: 20%;
      animation-delay: 4s;
    }
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@media (max-width: 480px) {
  .login-box {
    padding: 24px;
    
    .login-header h1 {
      font-size: 24px;
    }
  }
}
</style>

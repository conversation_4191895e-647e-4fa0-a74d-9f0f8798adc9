<template>
  <div class="profile-container">
    <div class="page-header">
      <h2>个人资料</h2>
      <p>查看和管理您的个人信息</p>
    </div>

    <el-row :gutter="20">
      <!-- 用户信息卡片 -->
      <el-col :xs="24" :lg="8">
        <el-card class="profile-card">
          <div class="profile-header">
            <el-avatar :size="80" :src="userInfo.avatar">
              <el-icon size="40"><User /></el-icon>
            </el-avatar>
            <div class="profile-info">
              <h3>{{ userInfo.username || '未知用户' }}</h3>
              <p>{{ userInfo.email || '未设置邮箱' }}</p>
            </div>
          </div>
          
          <div class="profile-stats">
            <div class="stat-item">
              <div class="stat-value">{{ userStats.totalProjects || 0 }}</div>
              <div class="stat-label">创建的项目</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ userStats.activeProjects || 0 }}</div>
              <div class="stat-label">进行中项目</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ userStats.completedProjects || 0 }}</div>
              <div class="stat-label">已完成项目</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 详细信息 -->
      <el-col :xs="24" :lg="16">
        <el-card class="info-card">
          <template #header>
            <div class="card-header">
              <span>基本信息</span>
            </div>
          </template>
          
          <el-descriptions :column="2" border>
            <el-descriptions-item label="用户名">
              {{ userInfo.username || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="邮箱">
              {{ userInfo.email || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="姓名">
              {{ profile.full_name || userInfo.name || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="Keycloak ID">
              {{ profile.keycloak_id || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="用户角色" :span="2">
              <el-tag
                v-for="role in userInfo.roles"
                :key="role"
                type="primary"
                size="small"
                class="role-tag"
              >
                {{ role }}
              </el-tag>
              <span v-if="!userInfo.roles || userInfo.roles.length === 0">-</span>
            </el-descriptions-item>
            <el-descriptions-item label="注册时间">
              {{ formatDateTime(profile.date_joined) }}
            </el-descriptions-item>
            <el-descriptions-item label="最后登录">
              {{ formatDateTime(profile.last_login) }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 最近活动 -->
        <el-card class="activity-card">
          <template #header>
            <div class="card-header">
              <span>最近活动</span>
              <el-button text @click="refreshActivities">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          
          <div class="activity-list">
            <div
              v-for="activity in recentActivities"
              :key="activity.id"
              class="activity-item"
            >
              <div class="activity-icon">
                <el-icon><Operation /></el-icon>
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ activity.action }}</div>
                <div class="activity-desc">
                  {{ activity.resource }}
                  <span v-if="activity.resource_id">- ID: {{ activity.resource_id }}</span>
                </div>
                <div class="activity-time">{{ formatDateTime(activity.timestamp) }}</div>
              </div>
            </div>
            
            <div v-if="!recentActivities.length" class="no-activity">
              <el-empty description="暂无活动记录" />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'

export default {
  name: 'UserProfile',
  setup() {
    const store = useStore()
    const loading = ref(false)
    const userStats = ref({})
    
    const userInfo = computed(() => store.getters['user/userInfo'] || {})
    const profile = computed(() => store.getters['user/profile'] || {})
    const recentActivities = computed(() => store.getters['user/recentActivities'] || [])
    
    const fetchUserData = async () => {
      loading.value = true
      try {
        // 获取用户信息
        await store.dispatch('user/fetchUserInfo')
        
        // 获取用户活动
        await store.dispatch('user/fetchUserActivities')
        
        // 获取用户统计信息（模拟数据）
        userStats.value = {
          totalProjects: 12,
          activeProjects: 3,
          completedProjects: 9
        }
        
      } catch (error) {
        console.error('Failed to fetch user data:', error)
        ElMessage.error('获取用户信息失败')
      } finally {
        loading.value = false
      }
    }
    
    const refreshActivities = async () => {
      try {
        await store.dispatch('user/fetchUserActivities')
        ElMessage.success('活动记录已刷新')
      } catch (error) {
        ElMessage.error('刷新失败')
      }
    }
    
    const formatDateTime = (datetime) => {
      if (!datetime) return '-'
      return dayjs(datetime).format('YYYY-MM-DD HH:mm:ss')
    }
    
    onMounted(() => {
      fetchUserData()
    })
    
    return {
      loading,
      userInfo,
      profile,
      userStats,
      recentActivities,
      refreshActivities,
      formatDateTime
    }
  }
}
</script>

<style lang="scss" scoped>
.profile-container {
  padding: 20px;
  
  .page-header {
    margin-bottom: 20px;
    
    h2 {
      margin: 0 0 4px 0;
      color: #303133;
      font-size: 24px;
    }
    
    p {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }
  
  .profile-card {
    .profile-header {
      text-align: center;
      padding-bottom: 20px;
      border-bottom: 1px solid #f0f0f0;
      
      .profile-info {
        margin-top: 16px;
        
        h3 {
          margin: 0 0 4px 0;
          color: #303133;
          font-size: 18px;
        }
        
        p {
          margin: 0;
          color: #909399;
          font-size: 14px;
        }
      }
    }
    
    .profile-stats {
      display: flex;
      justify-content: space-around;
      padding-top: 20px;
      
      .stat-item {
        text-align: center;
        
        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: #409EFF;
          margin-bottom: 4px;
        }
        
        .stat-label {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }
  
  .info-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .role-tag {
      margin-right: 8px;
      margin-bottom: 4px;
    }
  }
  
  .activity-card {
    .activity-list {
      max-height: 400px;
      overflow-y: auto;
      
      .activity-item {
        display: flex;
        align-items: flex-start;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }
        
        .activity-icon {
          width: 32px;
          height: 32px;
          background: #f5f7fa;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;
          flex-shrink: 0;
          
          .el-icon {
            color: #909399;
          }
        }
        
        .activity-content {
          flex: 1;
          
          .activity-title {
            font-size: 14px;
            color: #303133;
            margin-bottom: 4px;
            font-weight: 500;
          }
          
          .activity-desc {
            font-size: 12px;
            color: #909399;
            margin-bottom: 4px;
          }
          
          .activity-time {
            font-size: 12px;
            color: #c0c4cc;
          }
        }
      }
      
      .no-activity {
        padding: 20px 0;
      }
    }
  }
}

@media (max-width: 768px) {
  .profile-container {
    padding: 12px;
    
    .profile-card .profile-stats {
      flex-direction: column;
      gap: 16px;
    }
  }
}
</style>

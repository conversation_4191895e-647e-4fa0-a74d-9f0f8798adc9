<template>
  <div class="settings-container">
    <div class="page-header">
      <h2>系统设置</h2>
      <p>管理系统配置和参数</p>
    </div>

    <el-row :gutter="20">
      <!-- 设置菜单 -->
      <el-col :xs="24" :lg="6">
        <el-card class="menu-card">
          <el-menu
            v-model="activeMenu"
            class="settings-menu"
            @select="handleMenuSelect"
          >
            <el-menu-item index="general">
              <el-icon><Setting /></el-icon>
              <span>常规设置</span>
            </el-menu-item>
            <el-menu-item index="keycloak">
              <el-icon><Key /></el-icon>
              <span>认证配置</span>
            </el-menu-item>
            <el-menu-item index="database">
              <el-icon><Coin /></el-icon>
              <span>数据库配置</span>
            </el-menu-item>
            <el-menu-item index="file">
              <el-icon><Folder /></el-icon>
              <span>文件设置</span>
            </el-menu-item>
            <el-menu-item index="notification">
              <el-icon><Bell /></el-icon>
              <span>通知设置</span>
            </el-menu-item>
            <el-menu-item index="backup">
              <el-icon><Download /></el-icon>
              <span>备份恢复</span>
            </el-menu-item>
          </el-menu>
        </el-card>
      </el-col>

      <!-- 设置内容 -->
      <el-col :xs="24" :lg="18">
        <!-- 常规设置 -->
        <el-card v-show="activeMenu === 'general'" class="content-card">
          <template #header>
            <span>常规设置</span>
          </template>
          
          <el-form :model="generalSettings" label-width="120px">
            <el-form-item label="系统名称">
              <el-input v-model="generalSettings.systemName" placeholder="输入系统名称" />
            </el-form-item>
            
            <el-form-item label="系统描述">
              <el-input
                v-model="generalSettings.systemDescription"
                type="textarea"
                :rows="3"
                placeholder="输入系统描述"
              />
            </el-form-item>
            
            <el-form-item label="默认语言">
              <el-select v-model="generalSettings.defaultLanguage" placeholder="选择默认语言">
                <el-option label="中文" value="zh-CN" />
                <el-option label="English" value="en-US" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="时区设置">
              <el-select v-model="generalSettings.timezone" placeholder="选择时区">
                <el-option label="Asia/Shanghai" value="Asia/Shanghai" />
                <el-option label="UTC" value="UTC" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="启用调试模式">
              <el-switch v-model="generalSettings.debugMode" />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="saveGeneralSettings">保存设置</el-button>
              <el-button @click="resetGeneralSettings">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- Keycloak 配置 -->
        <el-card v-show="activeMenu === 'keycloak'" class="content-card">
          <template #header>
            <span>Keycloak 认证配置</span>
          </template>
          
          <el-form :model="keycloakSettings" label-width="120px">
            <el-form-item label="服务器地址">
              <el-input v-model="keycloakSettings.serverUrl" placeholder="Keycloak 服务器地址" />
            </el-form-item>
            
            <el-form-item label="Realm">
              <el-input v-model="keycloakSettings.realm" placeholder="Realm 名称" />
            </el-form-item>
            
            <el-form-item label="前端客户端ID">
              <el-input v-model="keycloakSettings.frontendClientId" placeholder="前端客户端ID" />
            </el-form-item>
            
            <el-form-item label="后端客户端ID">
              <el-input v-model="keycloakSettings.backendClientId" placeholder="后端客户端ID" />
            </el-form-item>
            
            <el-form-item label="客户端密钥">
              <el-input
                v-model="keycloakSettings.clientSecret"
                type="password"
                placeholder="客户端密钥"
                show-password
              />
            </el-form-item>
            
            <el-form-item label="启用 SSL">
              <el-switch v-model="keycloakSettings.sslRequired" />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="saveKeycloakSettings">保存配置</el-button>
              <el-button @click="testKeycloakConnection">测试连接</el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 数据库配置 -->
        <el-card v-show="activeMenu === 'database'" class="content-card">
          <template #header>
            <span>数据库配置</span>
          </template>
          
          <el-alert
            title="注意"
            type="warning"
            description="修改数据库配置需要重启系统才能生效，请谨慎操作。"
            show-icon
            :closable="false"
            style="margin-bottom: 20px;"
          />
          
          <el-form :model="databaseSettings" label-width="120px">
            <el-form-item label="数据库类型">
              <el-select v-model="databaseSettings.engine" placeholder="选择数据库类型">
                <el-option label="MySQL" value="mysql" />
                <el-option label="PostgreSQL" value="postgresql" />
                <el-option label="SQLite" value="sqlite" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="主机地址">
              <el-input v-model="databaseSettings.host" placeholder="数据库主机地址" />
            </el-form-item>
            
            <el-form-item label="端口">
              <el-input-number v-model="databaseSettings.port" :min="1" :max="65535" />
            </el-form-item>
            
            <el-form-item label="数据库名">
              <el-input v-model="databaseSettings.name" placeholder="数据库名称" />
            </el-form-item>
            
            <el-form-item label="用户名">
              <el-input v-model="databaseSettings.user" placeholder="数据库用户名" />
            </el-form-item>
            
            <el-form-item label="密码">
              <el-input
                v-model="databaseSettings.password"
                type="password"
                placeholder="数据库密码"
                show-password
              />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="saveDatabaseSettings">保存配置</el-button>
              <el-button @click="testDatabaseConnection">测试连接</el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 文件设置 -->
        <el-card v-show="activeMenu === 'file'" class="content-card">
          <template #header>
            <span>文件设置</span>
          </template>
          
          <el-form :model="fileSettings" label-width="120px">
            <el-form-item label="上传目录">
              <el-input v-model="fileSettings.uploadPath" placeholder="文件上传目录" />
            </el-form-item>
            
            <el-form-item label="最大文件大小">
              <el-input-number
                v-model="fileSettings.maxFileSize"
                :min="1"
                :max="1024"
                placeholder="MB"
              />
              <span style="margin-left: 8px;">MB</span>
            </el-form-item>
            
            <el-form-item label="允许的文件类型">
              <el-checkbox-group v-model="fileSettings.allowedTypes">
                <el-checkbox label="image">图片文件</el-checkbox>
                <el-checkbox label="document">文档文件</el-checkbox>
                <el-checkbox label="excel">Excel 文件</el-checkbox>
                <el-checkbox label="csv">CSV 文件</el-checkbox>
                <el-checkbox label="pdf">PDF 文件</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            
            <el-form-item label="启用文件压缩">
              <el-switch v-model="fileSettings.enableCompression" />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="saveFileSettings">保存设置</el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 通知设置 -->
        <el-card v-show="activeMenu === 'notification'" class="content-card">
          <template #header>
            <span>通知设置</span>
          </template>
          
          <el-form :model="notificationSettings" label-width="120px">
            <el-form-item label="启用邮件通知">
              <el-switch v-model="notificationSettings.emailEnabled" />
            </el-form-item>
            
            <el-form-item label="SMTP 服务器" v-if="notificationSettings.emailEnabled">
              <el-input v-model="notificationSettings.smtpHost" placeholder="SMTP 服务器地址" />
            </el-form-item>
            
            <el-form-item label="SMTP 端口" v-if="notificationSettings.emailEnabled">
              <el-input-number v-model="notificationSettings.smtpPort" :min="1" :max="65535" />
            </el-form-item>
            
            <el-form-item label="发送邮箱" v-if="notificationSettings.emailEnabled">
              <el-input v-model="notificationSettings.fromEmail" placeholder="发送邮箱地址" />
            </el-form-item>
            
            <el-form-item label="启用系统通知">
              <el-switch v-model="notificationSettings.systemEnabled" />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="saveNotificationSettings">保存设置</el-button>
              <el-button @click="testEmailNotification" v-if="notificationSettings.emailEnabled">
                发送测试邮件
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 备份恢复 -->
        <el-card v-show="activeMenu === 'backup'" class="content-card">
          <template #header>
            <span>备份恢复</span>
          </template>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <h4>数据备份</h4>
              <p>创建系统数据备份</p>
              <el-button type="primary" @click="createBackup" :loading="backupLoading">
                <el-icon><Download /></el-icon>
                创建备份
              </el-button>
            </el-col>
            
            <el-col :span="12">
              <h4>数据恢复</h4>
              <p>从备份文件恢复数据</p>
              <el-upload
                class="upload-demo"
                :auto-upload="false"
                :show-file-list="false"
                @change="handleBackupFile"
              >
                <el-button type="success">
                  <el-icon><Upload /></el-icon>
                  选择备份文件
                </el-button>
              </el-upload>
            </el-col>
          </el-row>
          
          <el-divider />
          
          <h4>备份历史</h4>
          <el-table :data="backupHistory" stripe>
            <el-table-column prop="filename" label="文件名" />
            <el-table-column prop="size" label="文件大小" />
            <el-table-column prop="created_at" label="创建时间" />
            <el-table-column label="操作" width="150">
              <template #default="{ row }">
                <el-button size="small" @click="downloadBackup(row)">下载</el-button>
                <el-button size="small" type="danger" @click="deleteBackup(row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

export default {
  name: 'SystemSettings',
  setup() {
    const activeMenu = ref('general')
    const backupLoading = ref(false)
    
    const generalSettings = reactive({
      systemName: 'NVH 测试数据管理系统',
      systemDescription: 'NVH (噪声、振动、声学) 测试数据管理系统',
      defaultLanguage: 'zh-CN',
      timezone: 'Asia/Shanghai',
      debugMode: false
    })
    
    const keycloakSettings = reactive({
      serverUrl: 'https://account-test.sgmw.com.cn/auth/',
      realm: 'demo',
      frontendClientId: 'front',
      backendClientId: 'backend',
      clientSecret: '8545c061-7cf7-41e5-b92b-e6769a6a75b8',
      sslRequired: true
    })
    
    const databaseSettings = reactive({
      engine: 'mysql',
      host: 'localhost',
      port: 3306,
      name: 'nvh_data',
      user: 'root',
      password: ''
    })
    
    const fileSettings = reactive({
      uploadPath: '/media/uploads/',
      maxFileSize: 16,
      allowedTypes: ['image', 'document', 'excel', 'csv'],
      enableCompression: true
    })
    
    const notificationSettings = reactive({
      emailEnabled: false,
      smtpHost: '',
      smtpPort: 587,
      fromEmail: '',
      systemEnabled: true
    })
    
    const backupHistory = ref([
      {
        filename: 'backup_2024_01_15.sql',
        size: '2.5 MB',
        created_at: '2024-01-15 10:30:00'
      },
      {
        filename: 'backup_2024_01_10.sql',
        size: '2.3 MB',
        created_at: '2024-01-10 09:15:00'
      }
    ])
    
    const handleMenuSelect = (index) => {
      activeMenu.value = index
    }
    
    const saveGeneralSettings = () => {
      ElMessage.success('常规设置保存成功')
    }
    
    const resetGeneralSettings = () => {
      // 重置为默认值
      ElMessage.info('设置已重置')
    }
    
    const saveKeycloakSettings = () => {
      ElMessage.success('Keycloak 配置保存成功')
    }
    
    const testKeycloakConnection = () => {
      ElMessage.info('正在测试 Keycloak 连接...')
      setTimeout(() => {
        ElMessage.success('Keycloak 连接测试成功')
      }, 2000)
    }
    
    const saveDatabaseSettings = () => {
      ElMessage.success('数据库配置保存成功')
    }
    
    const testDatabaseConnection = () => {
      ElMessage.info('正在测试数据库连接...')
      setTimeout(() => {
        ElMessage.success('数据库连接测试成功')
      }, 2000)
    }
    
    const saveFileSettings = () => {
      ElMessage.success('文件设置保存成功')
    }
    
    const saveNotificationSettings = () => {
      ElMessage.success('通知设置保存成功')
    }
    
    const testEmailNotification = () => {
      ElMessage.info('正在发送测试邮件...')
      setTimeout(() => {
        ElMessage.success('测试邮件发送成功')
      }, 2000)
    }
    
    const createBackup = () => {
      backupLoading.value = true
      setTimeout(() => {
        backupLoading.value = false
        ElMessage.success('备份创建成功')
      }, 3000)
    }
    
    const handleBackupFile = (file) => {
      ElMessage.info(`选择了备份文件: ${file.name}`)
    }
    
    const downloadBackup = (row) => {
      ElMessage.info(`下载备份: ${row.filename}`)
    }
    
    const deleteBackup = async (row) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除备份文件 "${row.filename}" 吗？`,
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        ElMessage.success('备份文件删除成功')
      } catch (error) {
        // 用户取消
      }
    }
    
    onMounted(() => {
      // 加载设置数据
    })
    
    return {
      activeMenu,
      backupLoading,
      generalSettings,
      keycloakSettings,
      databaseSettings,
      fileSettings,
      notificationSettings,
      backupHistory,
      handleMenuSelect,
      saveGeneralSettings,
      resetGeneralSettings,
      saveKeycloakSettings,
      testKeycloakConnection,
      saveDatabaseSettings,
      testDatabaseConnection,
      saveFileSettings,
      saveNotificationSettings,
      testEmailNotification,
      createBackup,
      handleBackupFile,
      downloadBackup,
      deleteBackup
    }
  }
}
</script>

<style lang="scss" scoped>
.settings-container {
  padding: 20px;
  
  .page-header {
    margin-bottom: 20px;
    
    h2 {
      margin: 0 0 4px 0;
      color: #303133;
      font-size: 24px;
    }
    
    p {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }
  
  .menu-card {
    .settings-menu {
      border: none;
      
      .el-menu-item {
        border-radius: 4px;
        margin-bottom: 4px;
        
        &:hover {
          background-color: #f5f7fa;
        }
        
        &.is-active {
          background-color: #409EFF;
          color: white;
          
          .el-icon {
            color: white;
          }
        }
      }
    }
  }
  
  .content-card {
    min-height: 500px;
    
    h4 {
      margin: 0 0 8px 0;
      color: #303133;
    }
    
    p {
      margin: 0 0 16px 0;
      color: #606266;
      font-size: 14px;
    }
  }
}

@media (max-width: 768px) {
  .settings-container {
    padding: 12px;
    
    .menu-card {
      margin-bottom: 20px;
    }
  }
}
</style>

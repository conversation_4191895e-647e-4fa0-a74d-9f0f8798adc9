<template>
  <div class="project-detail-container">
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" text>
          <el-icon><ArrowLeft /></el-icon>
          返回项目列表
        </el-button>
        <h2>{{ project?.name || '项目详情' }}</h2>
        <el-tag :type="getStatusType(project?.status)">
          {{ project?.status_display }}
        </el-tag>
      </div>
      <div class="header-actions">
        <el-button @click="editProject">
          <el-icon><Edit /></el-icon>
          编辑项目
        </el-button>
        <el-button type="primary" @click="addTestData">
          <el-icon><Plus /></el-icon>
          添加测试数据
        </el-button>
      </div>
    </div>

    <el-row :gutter="20" v-loading="loading">
      <!-- 项目信息 -->
      <el-col :xs="24" :lg="8">
        <el-card class="project-info-card">
          <template #header>
            <span>项目信息</span>
          </template>
          
          <el-descriptions :column="1" border>
            <el-descriptions-item label="项目名称">
              {{ project?.name || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="项目类型">
              {{ project?.project_type_display || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="项目状态">
              <el-tag :type="getStatusType(project?.status)">
                {{ project?.status_display || '-' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="车型">
              {{ project?.vehicle_model || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="测试地点">
              {{ project?.test_location || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="开始日期">
              {{ project?.start_date || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="结束日期">
              {{ project?.end_date || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="创建者">
              {{ project?.created_by_name || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ formatDateTime(project?.created_at) }}
            </el-descriptions-item>
          </el-descriptions>
          
          <div v-if="project?.description" class="project-description">
            <h4>项目描述</h4>
            <p>{{ project.description }}</p>
          </div>
          
          <div v-if="project?.notes" class="project-notes">
            <h4>备注信息</h4>
            <p>{{ project.notes }}</p>
          </div>
        </el-card>
      </el-col>

      <!-- 测试数据统计 -->
      <el-col :xs="24" :lg="16">
        <el-card class="stats-card">
          <template #header>
            <span>测试数据统计</span>
          </template>
          
          <el-row :gutter="16">
            <el-col :xs="12" :sm="6">
              <div class="stat-item">
                <div class="stat-number">{{ testStats.modal || 0 }}</div>
                <div class="stat-label">模态测试</div>
              </div>
            </el-col>
            <el-col :xs="12" :sm="6">
              <div class="stat-item">
                <div class="stat-number">{{ testStats.airtightness || 0 }}</div>
                <div class="stat-label">气密性测试</div>
              </div>
            </el-col>
            <el-col :xs="12" :sm="6">
              <div class="stat-item">
                <div class="stat-number">{{ testStats.sound_insulation || 0 }}</div>
                <div class="stat-label">隔音测试</div>
              </div>
            </el-col>
            <el-col :xs="12" :sm="6">
              <div class="stat-item">
                <div class="stat-number">{{ testStats.total || 0 }}</div>
                <div class="stat-label">总测试数</div>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 最近测试数据 -->
        <el-card class="recent-tests-card">
          <template #header>
            <div class="card-header">
              <span>最近测试数据</span>
              <el-button text @click="viewAllTests">查看全部</el-button>
            </div>
          </template>
          
          <el-table :data="recentTests" stripe>
            <el-table-column prop="test_type" label="测试类型" width="120" />
            <el-table-column prop="test_date" label="测试日期" width="120">
              <template #default="{ row }">
                {{ formatDate(row.test_date) }}
              </template>
            </el-table-column>
            <el-table-column prop="test_conditions" label="测试条件" min-width="150">
              <template #default="{ row }">
                <span v-if="row.test_conditions">
                  {{ Object.keys(row.test_conditions).length }} 项条件
                </span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column prop="created_by_name" label="测试人员" width="100" />
            <el-table-column label="操作" width="120">
              <template #default="{ row }">
                <el-button size="small" @click="viewTestDetail(row)">
                  查看详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <div v-if="!recentTests.length" class="no-data">
            <el-empty description="暂无测试数据" />
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'

export default {
  name: 'ProjectDetail',
  setup() {
    const route = useRoute()
    const router = useRouter()
    const store = useStore()
    
    const loading = ref(false)
    const testStats = ref({})
    const recentTests = ref([])
    
    const project = computed(() => store.state.testing.currentProject)
    
    const fetchProjectDetail = async () => {
      const projectId = route.params.id
      if (!projectId) {
        ElMessage.error('项目ID不存在')
        router.push('/testing/projects')
        return
      }
      
      loading.value = true
      try {
        await store.dispatch('testing/fetchProject', projectId)
        
        // 模拟获取测试统计数据
        testStats.value = {
          modal: 5,
          airtightness: 3,
          sound_insulation: 2,
          total: 10
        }
        
        // 模拟获取最近测试数据
        recentTests.value = [
          {
            id: 1,
            test_type: '模态测试',
            test_date: '2024-01-15',
            test_conditions: { frequency: '0-200Hz', temperature: '25°C' },
            created_by_name: '张三'
          },
          {
            id: 2,
            test_type: '气密性测试',
            test_date: '2024-01-14',
            test_conditions: { pressure: '50Pa', humidity: '60%' },
            created_by_name: '李四'
          }
        ]
      } catch (error) {
        ElMessage.error('获取项目详情失败')
        router.push('/testing/projects')
      } finally {
        loading.value = false
      }
    }
    
    const goBack = () => {
      router.push('/testing/projects')
    }
    
    const editProject = () => {
      ElMessage.info('编辑项目功能开发中')
    }
    
    const addTestData = () => {
      ElMessage.info('添加测试数据功能开发中')
    }
    
    const viewAllTests = () => {
      ElMessage.info('查看全部测试数据功能开发中')
    }
    
    const viewTestDetail = (row) => {
      ElMessage.info(`查看测试详情: ${row.test_type}`)
    }
    
    const getStatusType = (status) => {
      const typeMap = {
        draft: 'info',
        active: 'warning',
        completed: 'success',
        archived: 'info'
      }
      return typeMap[status] || 'info'
    }
    
    const formatDate = (date) => {
      return dayjs(date).format('YYYY-MM-DD')
    }
    
    const formatDateTime = (datetime) => {
      if (!datetime) return '-'
      return dayjs(datetime).format('YYYY-MM-DD HH:mm:ss')
    }
    
    onMounted(() => {
      fetchProjectDetail()
    })
    
    return {
      loading,
      project,
      testStats,
      recentTests,
      goBack,
      editProject,
      addTestData,
      viewAllTests,
      viewTestDetail,
      getStatusType,
      formatDate,
      formatDateTime
    }
  }
}
</script>

<style lang="scss" scoped>
.project-detail-container {
  padding: 20px;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    
    .header-left {
      h2 {
        margin: 8px 0 4px 0;
        color: #303133;
        font-size: 24px;
      }
      
      .el-tag {
        margin-left: 12px;
      }
    }
    
    .header-actions {
      .el-button + .el-button {
        margin-left: 12px;
      }
    }
  }
  
  .project-info-card {
    .project-description,
    .project-notes {
      margin-top: 20px;
      padding-top: 20px;
      border-top: 1px solid #f0f0f0;
      
      h4 {
        margin: 0 0 8px 0;
        color: #303133;
        font-size: 14px;
      }
      
      p {
        margin: 0;
        color: #606266;
        font-size: 14px;
        line-height: 1.5;
      }
    }
  }
  
  .stats-card {
    margin-bottom: 20px;
    
    .stat-item {
      text-align: center;
      padding: 16px;
      border: 1px solid #f0f0f0;
      border-radius: 8px;
      
      .stat-number {
        font-size: 24px;
        font-weight: bold;
        color: #409EFF;
        margin-bottom: 4px;
      }
      
      .stat-label {
        font-size: 12px;
        color: #909399;
      }
    }
  }
  
  .recent-tests-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .no-data {
      padding: 40px 0;
    }
  }
}

@media (max-width: 768px) {
  .project-detail-container {
    padding: 12px;
    
    .page-header {
      flex-direction: column;
      align-items: flex-start;
      
      .header-actions {
        margin-top: 16px;
        width: 100%;
        
        .el-button {
          width: 48%;
        }
      }
    }
  }
}
</style>

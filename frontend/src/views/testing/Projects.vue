<template>
  <div class="projects-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2>测试项目管理</h2>
        <p>管理和查看所有测试项目</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新建项目
        </el-button>
      </div>
    </div>

    <!-- 筛选区域 -->
    <el-card class="filter-card">
      <el-form :model="filterForm" inline>
        <el-form-item label="项目类型">
          <el-select v-model="filterForm.project_type" placeholder="选择项目类型" clearable>
            <el-option
              v-for="type in projectTypes"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="项目状态">
          <el-select v-model="filterForm.status" placeholder="选择状态" clearable>
            <el-option
              v-for="status in projectStatuses"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="车型">
          <el-input v-model="filterForm.vehicle_model" placeholder="输入车型" clearable />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 项目列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="projects"
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="name" label="项目名称" min-width="200">
          <template #default="{ row }">
            <el-link type="primary" @click="viewProject(row)">
              {{ row.name }}
            </el-link>
          </template>
        </el-table-column>
        
        <el-table-column prop="project_type_display" label="项目类型" width="120" />
        
        <el-table-column prop="status_display" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ row.status_display }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="vehicle_model" label="车型" width="120" />
        
        <el-table-column prop="start_date" label="开始日期" width="120" />
        
        <el-table-column prop="created_by_name" label="创建者" width="100" />
        
        <el-table-column prop="created_at" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewProject(row)">
              查看
            </el-button>
            <el-button size="small" type="primary" @click="editProject(row)">
              编辑
            </el-button>
            <el-button size="small" type="danger" @click="deleteProject(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建/编辑项目对话框 -->
    <project-form-dialog
      v-model="showCreateDialog"
      :project="currentProject"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import ProjectFormDialog from '@/components/dialogs/ProjectFormDialog.vue'
import dayjs from 'dayjs'

export default {
  name: 'TestProjects',
  components: {
    ProjectFormDialog
  },
  setup() {
    const store = useStore()
    const router = useRouter()
    
    const loading = ref(false)
    const showCreateDialog = ref(false)
    const currentProject = ref(null)
    const selectedProjects = ref([])
    
    const filterForm = reactive({
      project_type: '',
      status: '',
      vehicle_model: ''
    })
    
    const projects = computed(() => store.state.testing.projects)
    const pagination = computed(() => store.state.testing.pagination)
    
    const projectTypes = [
      { value: 'modal', label: '模态测试' },
      { value: 'airtightness', label: '气密性测试' },
      { value: 'sound_insulation', label: '隔音测试' },
      { value: 'sound_absorption', label: '吸音测试' },
      { value: 'sound_transmission', label: '传声测试' },
      { value: 'wall_mounted_transmission', label: '壁挂传声测试' },
      { value: 'material_porosity', label: '材料孔隙率测试' }
    ]
    
    const projectStatuses = [
      { value: 'draft', label: '草稿' },
      { value: 'active', label: '进行中' },
      { value: 'completed', label: '已完成' },
      { value: 'archived', label: '已归档' }
    ]
    
    const fetchProjects = async (params = {}) => {
      loading.value = true
      try {
        await store.dispatch('testing/fetchProjects', {
          page: pagination.value.page,
          page_size: pagination.value.pageSize,
          ...filterForm,
          ...params
        })
      } catch (error) {
        ElMessage.error('获取项目列表失败')
      } finally {
        loading.value = false
      }
    }
    
    const handleSearch = () => {
      fetchProjects({ page: 1 })
    }
    
    const handleReset = () => {
      Object.keys(filterForm).forEach(key => {
        filterForm[key] = ''
      })
      fetchProjects({ page: 1 })
    }
    
    const handleSizeChange = (size) => {
      fetchProjects({ page: 1, page_size: size })
    }
    
    const handleCurrentChange = (page) => {
      fetchProjects({ page })
    }
    
    const handleSelectionChange = (selection) => {
      selectedProjects.value = selection
    }
    
    const viewProject = (project) => {
      router.push(`/testing/projects/${project.id}`)
    }
    
    const editProject = (project) => {
      currentProject.value = project
      showCreateDialog.value = true
    }
    
    const deleteProject = async (project) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除项目 "${project.name}" 吗？`,
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        await store.dispatch('testing/deleteProject', project.id)
        ElMessage.success('删除成功')
        fetchProjects()
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('删除失败')
        }
      }
    }
    
    const handleFormSuccess = () => {
      showCreateDialog.value = false
      currentProject.value = null
      fetchProjects()
    }
    
    const getStatusType = (status) => {
      const typeMap = {
        draft: 'info',
        active: 'warning',
        completed: 'success',
        archived: 'info'
      }
      return typeMap[status] || 'info'
    }
    
    const formatDateTime = (datetime) => {
      return dayjs(datetime).format('YYYY-MM-DD HH:mm')
    }
    
    onMounted(() => {
      fetchProjects()
    })
    
    return {
      loading,
      showCreateDialog,
      currentProject,
      selectedProjects,
      filterForm,
      projects,
      pagination,
      projectTypes,
      projectStatuses,
      handleSearch,
      handleReset,
      handleSizeChange,
      handleCurrentChange,
      handleSelectionChange,
      viewProject,
      editProject,
      deleteProject,
      handleFormSuccess,
      getStatusType,
      formatDateTime
    }
  }
}
</script>

<style lang="scss" scoped>
.projects-container {
  padding: 20px;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    margin-bottom: 20px;
    
    .header-content {
      h2 {
        margin: 0 0 4px 0;
        color: #303133;
        font-size: 24px;
      }
      
      p {
        margin: 0;
        color: #606266;
        font-size: 14px;
      }
    }
  }
  
  .filter-card {
    margin-bottom: 20px;
  }
  
  .table-card {
    .pagination-container {
      margin-top: 20px;
      text-align: right;
    }
  }
}

@media (max-width: 768px) {
  .projects-container {
    padding: 12px;
    
    .page-header {
      flex-direction: column;
      align-items: flex-start;
      
      .header-actions {
        margin-top: 16px;
      }
    }
  }
}
</style>

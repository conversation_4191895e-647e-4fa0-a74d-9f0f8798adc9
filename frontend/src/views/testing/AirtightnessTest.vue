<template>
  <div class="airtightness-test-container">
    <div class="page-header">
      <h2>气密性测试</h2>
      <p>管理和查看气密性测试数据</p>
    </div>

    <!-- 快速统计 -->
    <el-row :gutter="20" class="stats-section">
      <el-col :xs="24" :sm="8">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="24" color="#409EFF"><DataAnalysis /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalTests }}</div>
              <div class="stat-label">总测试次数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="8">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="24" color="#67C23A"><WindPower /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.avgPressure }}</div>
              <div class="stat-label">平均压差 (Pa)</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="8">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="24" color="#E6A23C"><Odometer /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.avgFlowRate }}</div>
              <div class="stat-label">平均流量 (m³/h)</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 操作区域 -->
    <el-card class="action-card">
      <el-row :gutter="16">
        <el-col :xs="24" :sm="6">
          <el-button type="primary" @click="showCreateDialog = true" block>
            <el-icon><Plus /></el-icon>
            新建测试
          </el-button>
        </el-col>
        <el-col :xs="24" :sm="6">
          <el-button @click="importData" block>
            <el-icon><Upload /></el-icon>
            导入数据
          </el-button>
        </el-col>
        <el-col :xs="24" :sm="6">
          <el-button @click="exportData" block>
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
        </el-col>
        <el-col :xs="24" :sm="6">
          <el-button @click="generateReport" block>
            <el-icon><Document /></el-icon>
            生成报告
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 筛选区域 -->
    <el-card class="filter-card">
      <el-form :model="filterForm" inline>
        <el-form-item label="项目">
          <el-select v-model="filterForm.project" placeholder="选择项目" clearable>
            <el-option
              v-for="project in projects"
              :key="project.id"
              :label="project.name"
              :value="project.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="测试日期">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        
        <el-form-item label="压差范围">
          <el-input-number
            v-model="filterForm.minPressure"
            placeholder="最小压差"
            :min="0"
            size="small"
            style="width: 120px"
          />
          <span style="margin: 0 8px">-</span>
          <el-input-number
            v-model="filterForm.maxPressure"
            placeholder="最大压差"
            :min="0"
            size="small"
            style="width: 120px"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table v-loading="loading" :data="airtightnessData" stripe>
        <el-table-column prop="project_name" label="项目名称" min-width="150" />
        <el-table-column prop="test_date" label="测试日期" width="120">
          <template #default="{ row }">
            {{ formatDate(row.test_date) }}
          </template>
        </el-table-column>
        <el-table-column prop="pressure_difference" label="压差 (Pa)" width="100" />
        <el-table-column prop="air_flow_rate" label="流量 (m³/h)" width="120" />
        <el-table-column prop="temperature" label="温度 (°C)" width="100" />
        <el-table-column prop="humidity" label="湿度 (%)" width="100" />
        <el-table-column label="测试图片" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.test_images?.length" type="success" size="small">
              {{ row.test_images.length }} 张
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="created_by_name" label="测试人员" width="100" />
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewDetail(row)">
              查看
            </el-button>
            <el-button size="small" type="primary" @click="editData(row)">
              编辑
            </el-button>
            <el-button size="small" type="danger" @click="deleteData(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div v-if="!airtightnessData.length && !loading" class="no-data">
        <el-empty description="暂无气密性测试数据" />
      </div>
    </el-card>

    <!-- 创建测试对话框 -->
    <el-dialog v-model="showCreateDialog" title="新建气密性测试" width="600px">
      <el-form :model="testForm" :rules="testRules" ref="testFormRef" label-width="120px">
        <el-form-item label="选择项目" prop="project">
          <el-select v-model="testForm.project" placeholder="请选择项目">
            <el-option
              v-for="project in projects"
              :key="project.id"
              :label="project.name"
              :value="project.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="测试日期" prop="test_date">
          <el-date-picker
            v-model="testForm.test_date"
            type="datetime"
            placeholder="选择测试日期"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="压差 (Pa)" prop="pressure_difference">
              <el-input-number
                v-model="testForm.pressure_difference"
                :min="0"
                :precision="2"
                placeholder="输入压差值"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="流量 (m³/h)" prop="air_flow_rate">
              <el-input-number
                v-model="testForm.air_flow_rate"
                :min="0"
                :precision="2"
                placeholder="输入流量值"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="温度 (°C)" prop="temperature">
              <el-input-number
                v-model="testForm.temperature"
                :precision="1"
                placeholder="输入温度值"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="湿度 (%)" prop="humidity">
              <el-input-number
                v-model="testForm.humidity"
                :min="0"
                :max="100"
                :precision="1"
                placeholder="输入湿度值"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="备注" prop="notes">
          <el-input
            v-model="testForm.notes"
            type="textarea"
            :rows="3"
            placeholder="输入测试备注"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" :loading="creating" @click="handleCreate">
          创建
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'

export default {
  name: 'AirtightnessTest',
  setup() {
    const store = useStore()
    
    const loading = ref(false)
    const creating = ref(false)
    const showCreateDialog = ref(false)
    const testFormRef = ref(null)
    
    const stats = ref({
      totalTests: 0,
      avgPressure: 0,
      avgFlowRate: 0
    })
    
    const filterForm = reactive({
      project: '',
      dateRange: [],
      minPressure: null,
      maxPressure: null
    })
    
    const testForm = reactive({
      project: '',
      test_date: '',
      pressure_difference: null,
      air_flow_rate: null,
      temperature: null,
      humidity: null,
      notes: ''
    })
    
    const testRules = {
      project: [{ required: true, message: '请选择项目', trigger: 'change' }],
      test_date: [{ required: true, message: '请选择测试日期', trigger: 'change' }],
      pressure_difference: [{ required: true, message: '请输入压差值', trigger: 'blur' }],
      air_flow_rate: [{ required: true, message: '请输入流量值', trigger: 'blur' }]
    }
    
    const airtightnessData = computed(() => store.state.testing.airtightnessData || [])
    const projects = computed(() => store.state.testing.projects || [])
    
    const fetchData = async () => {
      loading.value = true
      try {
        await Promise.all([
          store.dispatch('testing/fetchAirtightnessData'),
          store.dispatch('testing/fetchProjects')
        ])
        
        // 计算统计数据
        const data = airtightnessData.value
        stats.value = {
          totalTests: data.length,
          avgPressure: data.length ? (data.reduce((sum, item) => sum + (item.pressure_difference || 0), 0) / data.length).toFixed(1) : 0,
          avgFlowRate: data.length ? (data.reduce((sum, item) => sum + (item.air_flow_rate || 0), 0) / data.length).toFixed(1) : 0
        }
      } catch (error) {
        ElMessage.error('获取数据失败')
      } finally {
        loading.value = false
      }
    }
    
    const handleSearch = () => {
      // 实现搜索逻辑
      ElMessage.info('搜索功能开发中')
    }
    
    const handleReset = () => {
      Object.keys(filterForm).forEach(key => {
        if (Array.isArray(filterForm[key])) {
          filterForm[key] = []
        } else {
          filterForm[key] = key.includes('Pressure') ? null : ''
        }
      })
      fetchData()
    }
    
    const handleCreate = async () => {
      if (!testFormRef.value) return
      
      try {
        await testFormRef.value.validate()
        creating.value = true
        
        // 这里应该调用创建 API
        await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟创建
        
        ElMessage.success('测试数据创建成功')
        showCreateDialog.value = false
        resetTestForm()
        fetchData()
      } catch (error) {
        if (error !== false) { // 不是表单验证错误
          ElMessage.error('创建失败')
        }
      } finally {
        creating.value = false
      }
    }
    
    const resetTestForm = () => {
      Object.keys(testForm).forEach(key => {
        if (key.includes('pressure') || key.includes('rate') || key.includes('temperature') || key.includes('humidity')) {
          testForm[key] = null
        } else {
          testForm[key] = ''
        }
      })
      if (testFormRef.value) {
        testFormRef.value.clearValidate()
      }
    }
    
    const viewDetail = (row) => {
      ElMessage.info(`查看详情: ${row.project_name}`)
    }
    
    const editData = (row) => {
      ElMessage.info(`编辑数据: ${row.project_name}`)
    }
    
    const deleteData = async (row) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除这条测试数据吗？`,
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        // 这里应该调用删除 API
        ElMessage.success('删除成功')
        fetchData()
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('删除失败')
        }
      }
    }
    
    const importData = () => {
      ElMessage.info('数据导入功能开发中')
    }
    
    const exportData = () => {
      ElMessage.info('数据导出功能开发中')
    }
    
    const generateReport = () => {
      ElMessage.info('报告生成功能开发中')
    }
    
    const formatDate = (date) => {
      return dayjs(date).format('YYYY-MM-DD')
    }
    
    onMounted(() => {
      fetchData()
    })
    
    return {
      loading,
      creating,
      showCreateDialog,
      testFormRef,
      stats,
      filterForm,
      testForm,
      testRules,
      airtightnessData,
      projects,
      handleSearch,
      handleReset,
      handleCreate,
      viewDetail,
      editData,
      deleteData,
      importData,
      exportData,
      generateReport,
      formatDate
    }
  }
}
</script>

<style lang="scss" scoped>
.airtightness-test-container {
  padding: 20px;
  
  .page-header {
    margin-bottom: 20px;
    
    h2 {
      margin: 0 0 4px 0;
      color: #303133;
      font-size: 24px;
    }
    
    p {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }
  
  .stats-section {
    margin-bottom: 20px;
    
    .stat-card {
      .stat-content {
        display: flex;
        align-items: center;
        
        .stat-icon {
          margin-right: 16px;
          width: 48px;
          height: 48px;
          background: #f5f7fa;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        
        .stat-info {
          .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 4px;
          }
          
          .stat-label {
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }
  }
  
  .action-card,
  .filter-card {
    margin-bottom: 20px;
  }
  
  .table-card {
    .no-data {
      padding: 40px 0;
    }
  }
}

@media (max-width: 768px) {
  .airtightness-test-container {
    padding: 12px;
  }
}
</style>

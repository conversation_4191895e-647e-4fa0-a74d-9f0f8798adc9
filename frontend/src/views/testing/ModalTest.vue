<template>
  <div class="modal-test-container">
    <div class="page-header">
      <h2>模态测试</h2>
      <p>管理和查看模态测试数据</p>
    </div>

    <!-- 功能区域 -->
    <el-card class="function-card">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :lg="6">
          <div class="function-item">
            <div class="function-icon">
              <el-icon size="32" color="#409EFF"><Upload /></el-icon>
            </div>
            <div class="function-content">
              <h4>数据上传</h4>
              <p>上传模态测试数据文件</p>
              <el-button size="small" @click="showUploadDialog = true">
                开始上传
              </el-button>
            </div>
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="12" :lg="6">
          <div class="function-item">
            <div class="function-icon">
              <el-icon size="32" color="#67C23A"><TrendCharts /></el-icon>
            </div>
            <div class="function-content">
              <h4>数据分析</h4>
              <p>分析模态测试结果</p>
              <el-button size="small" @click="showAnalysisDialog = true">
                开始分析
              </el-button>
            </div>
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="12" :lg="6">
          <div class="function-item">
            <div class="function-icon">
              <el-icon size="32" color="#E6A23C"><View /></el-icon>
            </div>
            <div class="function-content">
              <h4>结果查看</h4>
              <p>查看模态测试报告</p>
              <el-button size="small" @click="viewResults">
                查看报告
              </el-button>
            </div>
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="12" :lg="6">
          <div class="function-item">
            <div class="function-icon">
              <el-icon size="32" color="#F56C6C"><Download /></el-icon>
            </div>
            <div class="function-content">
              <h4>数据导出</h4>
              <p>导出测试数据和报告</p>
              <el-button size="small" @click="exportData">
                导出数据
              </el-button>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 最近测试数据 -->
    <el-card class="data-card">
      <template #header>
        <div class="card-header">
          <span>最近测试数据</span>
          <el-button text @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>
      
      <el-table v-loading="loading" :data="modalData" stripe>
        <el-table-column prop="project_name" label="项目名称" min-width="150" />
        <el-table-column prop="test_date" label="测试日期" width="120">
          <template #default="{ row }">
            {{ formatDate(row.test_date) }}
          </template>
        </el-table-column>
        <el-table-column prop="frequency_range" label="频率范围" width="120" />
        <el-table-column label="固有频率数量" width="120">
          <template #default="{ row }">
            {{ row.natural_frequencies?.length || 0 }}
          </template>
        </el-table-column>
        <el-table-column label="模态振型数量" width="120">
          <template #default="{ row }">
            {{ row.modal_shapes?.length || 0 }}
          </template>
        </el-table-column>
        <el-table-column prop="created_by_name" label="创建者" width="100" />
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewDetail(row)">
              查看
            </el-button>
            <el-button size="small" type="primary" @click="editData(row)">
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div v-if="!modalData.length && !loading" class="no-data">
        <el-empty description="暂无模态测试数据" />
      </div>
    </el-card>

    <!-- 上传对话框 -->
    <el-dialog v-model="showUploadDialog" title="上传模态测试数据" width="500px">
      <el-form :model="uploadForm" label-width="100px">
        <el-form-item label="选择项目">
          <el-select v-model="uploadForm.project_id" placeholder="请选择项目">
            <el-option
              v-for="project in projects"
              :key="project.id"
              :label="project.name"
              :value="project.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="测试日期">
          <el-date-picker
            v-model="uploadForm.test_date"
            type="datetime"
            placeholder="选择测试日期"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        
        <el-form-item label="频率范围">
          <el-input v-model="uploadForm.frequency_range" placeholder="例如: 0-200Hz" />
        </el-form-item>
        
        <el-form-item label="数据文件">
          <el-upload
            class="upload-demo"
            drag
            :auto-upload="false"
            :file-list="uploadForm.files"
            @change="handleFileChange"
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持 .csv, .xlsx, .txt 格式文件
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showUploadDialog = false">取消</el-button>
        <el-button type="primary" :loading="uploading" @click="handleUpload">
          上传
        </el-button>
      </template>
    </el-dialog>

    <!-- 分析对话框 -->
    <el-dialog v-model="showAnalysisDialog" title="模态数据分析" width="600px">
      <div class="analysis-content">
        <el-alert
          title="功能开发中"
          type="info"
          description="模态数据分析功能正在开发中，敬请期待。"
          show-icon
          :closable="false"
        />
      </div>
      
      <template #footer>
        <el-button @click="showAnalysisDialog = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'

export default {
  name: 'ModalTest',
  setup() {
    const store = useStore()
    
    const loading = ref(false)
    const uploading = ref(false)
    const showUploadDialog = ref(false)
    const showAnalysisDialog = ref(false)
    
    const uploadForm = reactive({
      project_id: '',
      test_date: '',
      frequency_range: '',
      files: []
    })
    
    const modalData = computed(() => store.state.testing.modalData || [])
    const projects = computed(() => store.state.testing.projects || [])
    
    const fetchData = async () => {
      loading.value = true
      try {
        await Promise.all([
          store.dispatch('testing/fetchModalData'),
          store.dispatch('testing/fetchProjects')
        ])
      } catch (error) {
        ElMessage.error('获取数据失败')
      } finally {
        loading.value = false
      }
    }
    
    const refreshData = () => {
      fetchData()
      ElMessage.success('数据已刷新')
    }
    
    const handleFileChange = (file, fileList) => {
      uploadForm.files = fileList
    }
    
    const handleUpload = async () => {
      if (!uploadForm.project_id) {
        ElMessage.warning('请选择项目')
        return
      }
      
      if (!uploadForm.test_date) {
        ElMessage.warning('请选择测试日期')
        return
      }
      
      uploading.value = true
      try {
        // 这里应该调用上传 API
        await new Promise(resolve => setTimeout(resolve, 2000)) // 模拟上传
        ElMessage.success('上传成功')
        showUploadDialog.value = false
        fetchData()
      } catch (error) {
        ElMessage.error('上传失败')
      } finally {
        uploading.value = false
      }
    }
    
    const viewDetail = (row) => {
      ElMessage.info(`查看详情: ${row.project_name}`)
    }
    
    const editData = (row) => {
      ElMessage.info(`编辑数据: ${row.project_name}`)
    }
    
    const viewResults = () => {
      ElMessage.info('查看测试报告功能开发中')
    }
    
    const exportData = () => {
      ElMessage.info('数据导出功能开发中')
    }
    
    const formatDate = (date) => {
      return dayjs(date).format('YYYY-MM-DD')
    }
    
    onMounted(() => {
      fetchData()
    })
    
    return {
      loading,
      uploading,
      showUploadDialog,
      showAnalysisDialog,
      uploadForm,
      modalData,
      projects,
      refreshData,
      handleFileChange,
      handleUpload,
      viewDetail,
      editData,
      viewResults,
      exportData,
      formatDate
    }
  }
}
</script>

<style lang="scss" scoped>
.modal-test-container {
  padding: 20px;
  
  .page-header {
    margin-bottom: 20px;
    
    h2 {
      margin: 0 0 4px 0;
      color: #303133;
      font-size: 24px;
    }
    
    p {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }
  
  .function-card {
    margin-bottom: 20px;
    
    .function-item {
      display: flex;
      align-items: center;
      padding: 16px;
      border: 1px solid #f0f0f0;
      border-radius: 8px;
      transition: all 0.3s;
      
      &:hover {
        border-color: #409EFF;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
      }
      
      .function-icon {
        margin-right: 16px;
        flex-shrink: 0;
      }
      
      .function-content {
        flex: 1;
        
        h4 {
          margin: 0 0 4px 0;
          color: #303133;
          font-size: 16px;
        }
        
        p {
          margin: 0 0 8px 0;
          color: #909399;
          font-size: 12px;
        }
      }
    }
  }
  
  .data-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .no-data {
      padding: 40px 0;
    }
  }
  
  .analysis-content {
    padding: 20px 0;
  }
}

@media (max-width: 768px) {
  .modal-test-container {
    padding: 12px;
    
    .function-card .function-item {
      flex-direction: column;
      text-align: center;
      
      .function-icon {
        margin-right: 0;
        margin-bottom: 12px;
      }
    }
  }
}
</style>

<template>
  <div class="home">
    <!-- Header -->
    <el-header class="header">
      <div class="header-content">
        <h1 class="title">NVH 数据管理系统</h1>
        <div class="user-info">
          <el-dropdown @command="handleCommand">
            <span class="user-name">
              {{ userInfo.name || userInfo.username }}
              <el-icon><arrow-down /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">个人信息</el-dropdown-item>
                <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </el-header>

    <!-- Main Content -->
    <el-main class="main-content">
      <div class="welcome-section">
        <el-card class="welcome-card">
          <h2>{{ dashboardData.welcome_message }}</h2>
          <p class="system-description">
            {{ dashboardData.system_info?.description }}
          </p>
        </el-card>
      </div>

      <!-- System Info Cards -->
      <div class="info-cards">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card class="info-card">
              <div class="card-content">
                <el-icon class="card-icon"><user /></el-icon>
                <div class="card-info">
                  <h3>当前用户</h3>
                  <p>{{ userInfo.username }}</p>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="info-card">
              <div class="card-content">
                <el-icon class="card-icon"><setting /></el-icon>
                <div class="card-info">
                  <h3>系统版本</h3>
                  <p>{{ dashboardData.system_info?.version }}</p>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="info-card">
              <div class="card-content">
                <el-icon class="card-icon"><data-line /></el-icon>
                <div class="card-info">
                  <h3>总用户数</h3>
                  <p>{{ dashboardData.user_stats?.total_users }}</p>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- Quick Actions -->
      <div class="quick-actions">
        <el-card>
          <template #header>
            <span>快速操作</span>
          </template>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-button type="primary" size="large" class="action-btn">
                <el-icon><plus /></el-icon>
                新建项目
              </el-button>
            </el-col>
            <el-col :span="6">
              <el-button type="success" size="large" class="action-btn">
                <el-icon><upload /></el-icon>
                上传数据
              </el-button>
            </el-col>
            <el-col :span="6">
              <el-button type="info" size="large" class="action-btn">
                <el-icon><document /></el-icon>
                查看报告
              </el-button>
            </el-col>
            <el-col :span="6">
              <el-button type="warning" size="large" class="action-btn">
                <el-icon><setting /></el-icon>
                系统设置
              </el-button>
            </el-col>
          </el-row>
        </el-card>
      </div>
    </el-main>
  </div>
</template>

<script>
import { ref, onMounted, inject } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowDown, User, Setting, DataLine, Plus, Upload, Document } from '@element-plus/icons-vue'
import { getUserInfo, getDashboardData } from '../api'
import { getUserInfo as getKeycloakUserInfo, logout } from '../utils/keycloak'

export default {
  name: 'Home',
  components: {
    ArrowDown,
    User,
    Setting,
    DataLine,
    Plus,
    Upload,
    Document
  },
  setup() {
    const keycloak = inject('keycloak')
    const userInfo = ref({})
    const dashboardData = ref({})
    const loading = ref(false)

    const loadUserInfo = async () => {
      try {
        // Get user info from Keycloak token
        const keycloakUserInfo = getKeycloakUserInfo()
        userInfo.value = keycloakUserInfo || {}
        
        // Get additional user info from backend
        const backendUserInfo = await getUserInfo()
        userInfo.value = { ...userInfo.value, ...backendUserInfo }
      } catch (error) {
        console.error('Failed to load user info:', error)
        ElMessage.error('获取用户信息失败')
      }
    }

    const loadDashboardData = async () => {
      try {
        const data = await getDashboardData()
        dashboardData.value = data
      } catch (error) {
        console.error('Failed to load dashboard data:', error)
        ElMessage.error('获取仪表板数据失败')
      }
    }

    const handleCommand = (command) => {
      switch (command) {
        case 'profile':
          ElMessage.info('个人信息功能开发中...')
          break
        case 'logout':
          logout()
          break
      }
    }

    onMounted(async () => {
      loading.value = true
      try {
        await Promise.all([loadUserInfo(), loadDashboardData()])
      } finally {
        loading.value = false
      }
    })

    return {
      userInfo,
      dashboardData,
      loading,
      handleCommand
    }
  }
}
</script>

<style scoped>
.home {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 20px;
}

.title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.user-name {
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 16px;
}

.main-content {
  padding: 20px;
}

.welcome-section {
  margin-bottom: 20px;
}

.welcome-card {
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.welcome-card :deep(.el-card__body) {
  padding: 40px;
}

.welcome-card h2 {
  margin: 0 0 10px 0;
  font-size: 28px;
  font-weight: 600;
}

.system-description {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

.info-cards {
  margin-bottom: 20px;
}

.info-card {
  height: 120px;
}

.card-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.card-icon {
  font-size: 40px;
  color: #409eff;
  margin-right: 20px;
}

.card-info h3 {
  margin: 0 0 5px 0;
  font-size: 16px;
  color: #303133;
}

.card-info p {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #409eff;
}

.quick-actions {
  margin-bottom: 20px;
}

.action-btn {
  width: 100%;
  height: 60px;
  font-size: 16px;
}
</style>

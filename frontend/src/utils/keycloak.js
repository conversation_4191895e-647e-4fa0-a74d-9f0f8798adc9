import Keycloak from 'keycloak-js'

// Keycloak configuration
const keycloakConfig = {
  url: 'https://account-test.sgmw.com.cn/auth/',
  realm: 'demo',
  clientId: 'front'
}

let keycloakInstance = null

export const initKeycloak = () => {
  return new Promise((resolve, reject) => {
    if (keycloakInstance) {
      resolve(keycloakInstance)
      return
    }

    keycloakInstance = new Keycloak(keycloakConfig)

    keycloakInstance.init({
      onLoad: 'login-required',
      checkLoginIframe: false,
      pkceMethod: 'S256'
    }).then((authenticated) => {
      if (!authenticated) {
        console.warn('User not authenticated')
        reject(new Error('User not authenticated'))
      } else {
        console.log('User authenticated successfully')
        
        // Set up token refresh
        setInterval(() => {
          keycloakInstance.updateToken(70).then((refreshed) => {
            if (refreshed) {
              console.log('Token refreshed')
            } else {
              console.log('Token still valid')
            }
          }).catch((error) => {
            console.error('Failed to refresh token', error)
          })
        }, 60000) // Check every minute
        
        resolve(keycloakInstance)
      }
    }).catch((error) => {
      console.error('Keycloak initialization failed', error)
      reject(error)
    })
  })
}

export const getKeycloak = () => {
  return keycloakInstance
}

export const logout = () => {
  if (keycloakInstance) {
    keycloakInstance.logout()
  }
}

export const getToken = () => {
  return keycloakInstance ? keycloakInstance.token : null
}

export const getUserInfo = () => {
  if (!keycloakInstance) return null
  
  return {
    username: keycloakInstance.tokenParsed?.preferred_username,
    email: keycloakInstance.tokenParsed?.email,
    name: keycloakInstance.tokenParsed?.name,
    firstName: keycloakInstance.tokenParsed?.given_name,
    lastName: keycloakInstance.tokenParsed?.family_name,
    roles: keycloakInstance.tokenParsed?.realm_access?.roles || []
  }
}

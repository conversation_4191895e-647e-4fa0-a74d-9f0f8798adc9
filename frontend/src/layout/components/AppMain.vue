<template>
  <section class="app-main">
    <transition name="fade-transform" mode="out-in">
      <keep-alive :include="cachedViews">
        <router-view :key="key" />
      </keep-alive>
    </transition>
  </section>
</template>

<script>
import { computed } from 'vue'
import { useRoute } from 'vue-router'

export default {
  name: 'AppMain',
  setup() {
    const route = useRoute()
    
    const cachedViews = computed(() => {
      // 这里可以配置需要缓存的视图
      return ['Home', 'TestProjects']
    })
    
    const key = computed(() => {
      return route.path
    })
    
    return {
      cachedViews,
      key
    }
  }
}
</script>

<style lang="scss" scoped>
.app-main {
  min-height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  overflow: hidden;
  padding: 16px;
  background-color: #f5f5f5;
  margin-top: 50px;
}

.fixed-header + .app-main {
  padding-top: 50px;
}

.hasTagsView .app-main {
  min-height: calc(100vh - 84px);
}

.hasTagsView .fixed-header + .app-main {
  padding-top: 84px;
}
</style>

<style lang="scss">
// fade-transform transition
.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all 0.5s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
</style>

<template>
  <div :class="{ 'has-logo': showLogo }">
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="variables.menuBg"
        :text-color="variables.menuText"
        :unique-opened="false"
        :active-text-color="variables.menuActiveText"
        :collapse-transition="false"
        mode="vertical"
      >
        <sidebar-item
          v-for="route in routes"
          :key="route.path"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'
import Logo from './Logo.vue'
import SidebarItem from './SidebarItem.vue'
import variables from '@/styles/variables.scss'

export default {
  name: 'Sidebar',
  components: {
    Logo,
    SidebarItem
  },
  setup() {
    const route = useRoute()
    const store = useStore()
    
    const routes = computed(() => {
      // 这里返回路由配置，过滤掉隐藏的路由
      return [
        {
          path: '/',
          meta: { title: '首页', icon: 'House' },
          children: [
            {
              path: 'home',
              name: 'Home',
              meta: { title: '首页', icon: 'House' }
            }
          ]
        },
        {
          path: '/testing',
          meta: { title: '测试管理', icon: 'DataAnalysis' },
          children: [
            {
              path: 'projects',
              name: 'TestProjects',
              meta: { title: '测试项目', icon: 'FolderOpened' }
            },
            {
              path: 'modal',
              name: 'ModalTest',
              meta: { title: '模态测试', icon: 'TrendCharts' }
            },
            {
              path: 'airtightness',
              name: 'AirtightnessTest',
              meta: { title: '气密性测试', icon: 'WindPower' }
            }
          ]
        },
        {
          path: '/system',
          meta: { title: '系统管理', icon: 'Setting' },
          children: [
            {
              path: 'profile',
              name: 'UserProfile',
              meta: { title: '个人资料', icon: 'User' }
            },
            {
              path: 'settings',
              name: 'SystemSettings',
              meta: { title: '系统设置', icon: 'Tools' }
            }
          ]
        }
      ]
    })
    
    const sidebar = computed(() => store.getters['app/sidebar'])
    const device = computed(() => store.getters['app/device'])
    
    const activeMenu = computed(() => {
      const { meta, path } = route
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    })
    
    const showLogo = computed(() => true)
    
    const isCollapse = computed(() => !sidebar.value.opened)
    
    return {
      routes,
      activeMenu,
      showLogo,
      isCollapse,
      variables
    }
  }
}
</script>

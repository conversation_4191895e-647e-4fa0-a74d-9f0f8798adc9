<template>
  <el-breadcrumb class="app-breadcrumb" separator="/">
    <transition-group name="breadcrumb">
      <el-breadcrumb-item v-for="(item, index) in levelList" :key="item.path">
        <span
          v-if="item.redirect === 'noRedirect' || index === levelList.length - 1"
          class="no-redirect"
        >
          {{ item.meta.title }}
        </span>
        <a v-else @click.prevent="handleLink(item)">
          {{ item.meta.title }}
        </a>
      </el-breadcrumb-item>
    </transition-group>
  </el-breadcrumb>
</template>

<script>
import { ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

export default {
  name: 'Breadcrumb',
  setup() {
    const route = useRoute()
    const router = useRouter()
    const levelList = ref([])
    
    const getBreadcrumb = () => {
      // 过滤掉没有 meta.title 的路由
      let matched = route.matched.filter(item => item.meta && item.meta.title)
      
      const first = matched[0]
      
      // 如果第一个不是首页，则添加首页
      if (!isDashboard(first)) {
        matched = [{ path: '/home', meta: { title: '首页' } }].concat(matched)
      }
      
      levelList.value = matched.filter(item => {
        return item.meta && item.meta.title && item.meta.breadcrumb !== false
      })
    }
    
    const isDashboard = (route) => {
      const name = route && route.name
      if (!name) {
        return false
      }
      return name.trim().toLocaleLowerCase() === 'Home'.toLocaleLowerCase()
    }
    
    const pathCompile = (path) => {
      const { params } = route
      const toPath = path.replace(/:(\w+)/g, (match, key) => {
        return params[key] || match
      })
      return toPath
    }
    
    const handleLink = (item) => {
      const { redirect, path } = item
      if (redirect) {
        router.push(redirect)
        return
      }
      router.push(pathCompile(path))
    }
    
    // 监听路由变化
    watch(route, getBreadcrumb, { immediate: true })
    
    return {
      levelList,
      handleLink
    }
  }
}
</script>

<style lang="scss" scoped>
.app-breadcrumb.el-breadcrumb {
  display: inline-block;
  font-size: 14px;
  line-height: 50px;
  margin-left: 8px;
  
  .no-redirect {
    color: #97a8be;
    cursor: text;
  }
}
</style>

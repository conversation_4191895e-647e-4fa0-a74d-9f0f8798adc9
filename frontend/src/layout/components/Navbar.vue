<template>
  <div class="navbar">
    <hamburger
      id="hamburger-container"
      :is-active="sidebar.opened"
      class="hamburger-container"
      @toggleClick="toggleSideBar"
    />
    
    <breadcrumb id="breadcrumb-container" class="breadcrumb-container" />
    
    <div class="right-menu">
      <template v-if="device !== 'mobile'">
        <!-- 全屏按钮 -->
        <screenfull id="screenfull" class="right-menu-item hover-effect" />
        
        <!-- 消息通知 -->
        <el-badge :value="12" class="right-menu-item">
          <el-icon class="hover-effect" size="18">
            <Bell />
          </el-icon>
        </el-badge>
      </template>
      
      <!-- 用户下拉菜单 -->
      <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click">
        <div class="avatar-wrapper">
          <el-avatar :size="32" :src="avatar">
            <el-icon><User /></el-icon>
          </el-avatar>
          <span class="username">{{ username }}</span>
          <el-icon class="el-icon-caret-bottom">
            <CaretBottom />
          </el-icon>
        </div>
        
        <template #dropdown>
          <el-dropdown-menu>
            <router-link to="/system/profile">
              <el-dropdown-item>
                <el-icon><User /></el-icon>
                个人资料
              </el-dropdown-item>
            </router-link>
            
            <router-link to="/system/settings">
              <el-dropdown-item>
                <el-icon><Setting /></el-icon>
                系统设置
              </el-dropdown-item>
            </router-link>
            
            <el-dropdown-item divided @click="logout">
              <el-icon><SwitchButton /></el-icon>
              退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useStore } from 'vuex'
import { ElMessage } from 'element-plus'
import Breadcrumb from './Breadcrumb.vue'
import Hamburger from './Hamburger.vue'
import Screenfull from './Screenfull.vue'

export default {
  name: 'Navbar',
  components: {
    Breadcrumb,
    Hamburger,
    Screenfull
  },
  setup() {
    const store = useStore()
    
    const sidebar = computed(() => store.getters['app/sidebar'])
    const device = computed(() => store.getters['app/device'])
    const username = computed(() => store.getters['user/username'])
    const avatar = computed(() => store.getters['user/avatar'] || '')
    
    const toggleSideBar = () => {
      store.dispatch('app/toggleSideBar')
    }
    
    const logout = async () => {
      try {
        await store.dispatch('user/logout')
        ElMessage.success('退出登录成功')
      } catch (error) {
        console.error('Logout error:', error)
        ElMessage.error('退出登录失败')
      }
    }
    
    return {
      sidebar,
      device,
      username,
      avatar,
      toggleSideBar,
      logout
    }
  }
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  display: flex;
  align-items: center;
  
  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;
    
    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }
  
  .breadcrumb-container {
    float: left;
    margin-left: 16px;
  }
  
  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;
    display: flex;
    align-items: center;
    
    &:focus {
      outline: none;
    }
    
    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;
      
      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;
        
        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }
    
    .avatar-container {
      margin-right: 16px;
      
      .avatar-wrapper {
        display: flex;
        align-items: center;
        cursor: pointer;
        
        .username {
          margin-left: 8px;
          margin-right: 4px;
          font-size: 14px;
          color: #606266;
        }
        
        .el-icon-caret-bottom {
          font-size: 12px;
        }
      }
    }
  }
}
</style>

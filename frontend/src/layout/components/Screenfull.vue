<template>
  <div @click="click">
    <el-icon :size="18">
      <FullScreen v-if="!isFullscreen" />
      <Aim v-else />
    </el-icon>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'

export default {
  name: 'Screenfull',
  setup() {
    const isFullscreen = ref(false)
    
    const isEnabled = () => {
      return !!(
        document.fullscreenEnabled ||
        document.mozFullScreenEnabled ||
        document.webkitFullscreenEnabled ||
        document.msFullscreenEnabled
      )
    }
    
    const getElement = () => {
      return document.documentElement
    }
    
    const request = () => {
      const element = getElement()
      if (element.requestFullscreen) {
        element.requestFullscreen()
      } else if (element.mozRequestFullScreen) {
        element.mozRequestFullScreen()
      } else if (element.webkitRequestFullscreen) {
        element.webkitRequestFullscreen()
      } else if (element.msRequestFullscreen) {
        element.msRequestFullscreen()
      }
    }
    
    const exit = () => {
      if (document.exitFullscreen) {
        document.exitFullscreen()
      } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen()
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen()
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen()
      }
    }
    
    const toggle = () => {
      if (isFullscreen.value) {
        exit()
      } else {
        request()
      }
    }
    
    const change = () => {
      isFullscreen.value = !!(
        document.fullscreenElement ||
        document.mozFullScreenElement ||
        document.webkitFullscreenElement ||
        document.msFullscreenElement
      )
    }
    
    const click = () => {
      if (!isEnabled()) {
        ElMessage.warning('您的浏览器不支持全屏功能')
        return
      }
      toggle()
    }
    
    onMounted(() => {
      if (isEnabled()) {
        document.addEventListener('fullscreenchange', change)
        document.addEventListener('mozfullscreenchange', change)
        document.addEventListener('webkitfullscreenchange', change)
        document.addEventListener('msfullscreenchange', change)
      }
    })
    
    onUnmounted(() => {
      document.removeEventListener('fullscreenchange', change)
      document.removeEventListener('mozfullscreenchange', change)
      document.removeEventListener('webkitfullscreenchange', change)
      document.removeEventListener('msfullscreenchange', change)
    })
    
    return {
      isFullscreen,
      click
    }
  }
}
</script>

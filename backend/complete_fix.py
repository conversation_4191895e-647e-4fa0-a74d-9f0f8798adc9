#!/usr/bin/env python
"""
Django 迁移问题完整修复脚本
这个脚本将彻底清理数据库中的Django表并重新创建
"""

import os
import sys
import django
from django.conf import settings
from django.db import connection
from django.core.management import execute_from_command_line

def setup_django():
    """设置Django环境"""
    # 确保在正确的目录中
    if not os.path.exists('manage.py'):
        print("错误: 请在Django项目的backend目录中运行此脚本")
        sys.exit(1)

    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'nvh_django.settings')
    django.setup()

def clean_database():
    """清理数据库中的Django表"""
    print("正在清理数据库中的Django表...")
    
    with connection.cursor() as cursor:
        # 禁用外键检查
        cursor.execute("SET FOREIGN_KEY_CHECKS = 0")
        
        # 获取所有表名
        cursor.execute("SHOW TABLES")
        all_tables = [table[0] for table in cursor.fetchall()]
        
        # 删除所有Django相关的表
        django_tables = [table for table in all_tables if 
                        table.startswith(('django_', 'auth_')) or 
                        table in ['core_audit_log', 'core_system_config']]
        
        for table in django_tables:
            try:
                cursor.execute(f"DROP TABLE IF EXISTS {table}")
                print(f"  ✓ 删除表: {table}")
            except Exception as e:
                print(f"  ✗ 删除表失败 {table}: {e}")
        
        # 重新启用外键检查
        cursor.execute("SET FOREIGN_KEY_CHECKS = 1")
        
    print("数据库清理完成!")

def recreate_migrations():
    """重新创建迁移文件"""
    print("正在重新创建迁移文件...")
    
    # 删除现有迁移文件
    migration_files = [
        'apps/authentication/migrations/0001_initial.py',
        'apps/core/migrations/0001_initial.py', 
        'apps/testing/migrations/0001_initial.py'
    ]
    
    for file_path in migration_files:
        if os.path.exists(file_path):
            os.remove(file_path)
            print(f"  ✓ 删除迁移文件: {file_path}")
    
    # 创建新的迁移文件
    try:
        execute_from_command_line(['manage.py', 'makemigrations'])
        print("  ✓ 迁移文件创建成功")
    except Exception as e:
        print(f"  ✗ 创建迁移文件失败: {e}")
        return False
    
    return True

def apply_migrations():
    """应用迁移"""
    print("正在应用迁移...")
    
    try:
        execute_from_command_line(['manage.py', 'migrate'])
        print("  ✓ 迁移应用成功")
        return True
    except Exception as e:
        print(f"  ✗ 应用迁移失败: {e}")
        return False

def create_static_directory():
    """创建静态文件目录"""
    print("正在创建静态文件目录...")
    
    static_dir = 'static'
    if not os.path.exists(static_dir):
        os.makedirs(static_dir)
        print(f"  ✓ 创建目录: {static_dir}")
    else:
        print(f"  ✓ 目录已存在: {static_dir}")

def verify_setup():
    """验证设置"""
    print("正在验证设置...")
    
    try:
        execute_from_command_line(['manage.py', 'check'])
        print("  ✓ Django检查通过")
        return True
    except Exception as e:
        print(f"  ✗ Django检查失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("Django 迁移问题修复脚本")
    print("=" * 50)
    
    # 设置Django环境
    setup_django()
    
    # 步骤1: 清理数据库
    clean_database()
    
    # 步骤2: 创建静态文件目录
    create_static_directory()
    
    # 步骤3: 重新创建迁移文件
    if not recreate_migrations():
        print("迁移文件创建失败，请检查模型定义")
        return False
    
    # 步骤4: 应用迁移
    if not apply_migrations():
        print("迁移应用失败，请检查数据库连接")
        return False
    
    # 步骤5: 验证设置
    if not verify_setup():
        print("验证失败，请检查配置")
        return False
    
    print("=" * 50)
    print("✓ 修复完成！现在可以启动Django服务器了")
    print("运行命令: python manage.py runserver")
    print("=" * 50)
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)

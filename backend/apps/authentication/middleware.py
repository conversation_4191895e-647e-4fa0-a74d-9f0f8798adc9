import logging
from django.http import JsonResponse
from django.utils.deprecation import MiddlewareMixin
from rest_framework import status

logger = logging.getLogger(__name__)


class KeycloakAuthenticationMiddleware(MiddlewareMixin):
    """Keycloak 认证中间件"""
    
    # 不需要认证的路径
    EXCLUDED_PATHS = [
        '/admin/',
        '/api/auth/config/',
        '/api/auth/health/',
        '/static/',
        '/media/',
        '/favicon.ico',
    ]
    
    def process_request(self, request):
        """处理请求"""
        # 检查是否需要跳过认证
        path = request.path
        
        # 跳过不需要认证的路径
        for excluded_path in self.EXCLUDED_PATHS:
            if path.startswith(excluded_path):
                return None
        
        # 对于 API 请求，让 DRF 的认证系统处理
        if path.startswith('/api/'):
            return None
        
        return None
    
    def process_exception(self, request, exception):
        """处理异常"""
        if hasattr(exception, 'status_code') and exception.status_code == 401:
            return JsonResponse(
                {'error': 'Authentication required', 'message': str(exception)},
                status=status.HTTP_401_UNAUTHORIZED
            )
        return None

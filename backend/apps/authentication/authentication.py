import jwt
import requests
from django.conf import settings
from django.contrib.auth import get_user_model
from rest_framework import authentication, exceptions
from rest_framework.authentication import BaseAuthentication
import logging

logger = logging.getLogger(__name__)
User = get_user_model()


class KeycloakAuthentication(BaseAuthentication):
    """Keycloak JWT Token Authentication"""
    
    def __init__(self):
        self.keycloak_config = settings.KEYCLOAK_CONFIG
        self._public_key = None
        self._well_known_config = None
    
    def get_well_known_config(self):
        """获取 Keycloak well-known 配置"""
        if not self._well_known_config:
            try:
                response = requests.get(self.keycloak_config['KEYCLOAK_WELL_KNOWN_URL'])
                response.raise_for_status()
                self._well_known_config = response.json()
            except Exception as e:
                logger.error(f"Failed to fetch Keycloak well-known config: {e}")
                raise exceptions.AuthenticationFailed('Unable to fetch Keycloak configuration')
        return self._well_known_config
    
    def get_public_key(self):
        """获取 Keycloak 公钥"""
        if not self._public_key:
            try:
                well_known = self.get_well_known_config()
                jwks_uri = well_known['jwks_uri']
                response = requests.get(jwks_uri)
                response.raise_for_status()
                jwks = response.json()
                
                # 获取第一个密钥（通常是 RSA256）
                if jwks['keys']:
                    key_data = jwks['keys'][0]
                    # 构建公钥
                    from cryptography.hazmat.primitives import serialization
                    from cryptography.hazmat.primitives.asymmetric import rsa
                    import base64
                    
                    n = base64.urlsafe_b64decode(key_data['n'] + '==')
                    e = base64.urlsafe_b64decode(key_data['e'] + '==')
                    
                    public_numbers = rsa.RSAPublicNumbers(
                        int.from_bytes(e, 'big'),
                        int.from_bytes(n, 'big')
                    )
                    public_key = public_numbers.public_key()
                    self._public_key = public_key.public_bytes(
                        encoding=serialization.Encoding.PEM,
                        format=serialization.PublicFormat.SubjectPublicKeyInfo
                    )
            except Exception as e:
                logger.error(f"Failed to fetch Keycloak public key: {e}")
                raise exceptions.AuthenticationFailed('Unable to fetch Keycloak public key')
        return self._public_key
    
    def authenticate(self, request):
        """认证请求"""
        auth_header = request.META.get('HTTP_AUTHORIZATION')
        if not auth_header or not auth_header.startswith('Bearer '):
            return None
        
        token = auth_header.split(' ')[1]
        
        try:
            # 验证 JWT token
            public_key = self.get_public_key()
            payload = jwt.decode(
                token,
                public_key,
                algorithms=['RS256'],
                audience=self.keycloak_config['KEYCLOAK_CLIENT_ID'],
                issuer=f"{self.keycloak_config['KEYCLOAK_SERVER_URL']}realms/{self.keycloak_config['KEYCLOAK_REALM']}"
            )
            
            # 获取或创建用户
            user = self.get_or_create_user(payload)
            return (user, token)
            
        except jwt.ExpiredSignatureError:
            raise exceptions.AuthenticationFailed('Token has expired')
        except jwt.InvalidTokenError as e:
            logger.error(f"Invalid token: {e}")
            raise exceptions.AuthenticationFailed('Invalid token')
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            raise exceptions.AuthenticationFailed('Authentication failed')
    
    def get_or_create_user(self, payload):
        """根据 JWT payload 获取或创建用户"""
        keycloak_id = payload.get('sub')
        username = payload.get('preferred_username')
        email = payload.get('email', '')
        full_name = payload.get('name', '')
        roles = payload.get('realm_access', {}).get('roles', [])
        
        try:
            # 尝试通过 keycloak_id 查找用户
            user = User.objects.get(keycloak_id=keycloak_id)
            # 更新用户信息
            user.email = email
            user.full_name = full_name
            user.roles = roles
            user.save()
        except User.DoesNotExist:
            # 创建新用户
            user = User.objects.create(
                username=username,
                keycloak_id=keycloak_id,
                email=email,
                full_name=full_name,
                roles=roles,
                is_active=True
            )
        
        return user

from django.conf import settings
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView
import logging

logger = logging.getLogger(__name__)


@api_view(['GET'])
@permission_classes([AllowAny])
def keycloak_config(request):
    """获取 Keycloak 前端配置"""
    config = {
        'url': settings.KEYCLOAK_CONFIG['KEYCLOAK_SERVER_URL'],
        'realm': settings.KEYCLOAK_CONFIG['KEYCLOAK_REALM'],
        'clientId': settings.KEYCLOAK_CONFIG['KEYCLOAK_FRONTEND_CLIENT_ID'],
    }
    return Response(config)


@api_view(['GET'])
@permission_classes([AllowAny])
def health_check(request):
    """健康检查端点"""
    return Response({'status': 'ok', 'message': 'Authentication service is running'})


class UserProfileView(APIView):
    """用户信息视图"""
    
    def get(self, request):
        """获取当前用户信息"""
        if not request.user.is_authenticated:
            return Response(
                {'error': 'Authentication required'},
                status=status.HTTP_401_UNAUTHORIZED
            )
        
        user_data = {
            'id': request.user.id,
            'username': request.user.username,
            'email': request.user.email,
            'full_name': request.user.full_name,
            'roles': request.user.roles,
            'keycloak_id': request.user.keycloak_id,
            'is_active': request.user.is_active,
            'date_joined': request.user.date_joined,
        }
        
        return Response(user_data)


class LogoutView(APIView):
    """登出视图"""
    
    def post(self, request):
        """处理登出请求"""
        # 在前后端分离架构中，登出主要由前端处理
        # 后端可以记录登出日志或执行清理操作
        logger.info(f"User {request.user.username if request.user.is_authenticated else 'Anonymous'} logged out")
        
        return Response({'message': 'Logout successful'})

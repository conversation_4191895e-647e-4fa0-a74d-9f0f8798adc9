from rest_framework import serializers
from .models import TestProject, Vehicle, Component, ModalTestData, AirtightnessTestData


class TestProjectSerializer(serializers.ModelSerializer):
    """测试项目序列化器"""
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    updated_by_name = serializers.CharField(source='updated_by.username', read_only=True)
    project_type_display = serializers.CharField(source='get_project_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = TestProject
        fields = [
            'id', 'name', 'description', 'project_type', 'project_type_display',
            'status', 'status_display', 'start_date', 'end_date', 'vehicle_model',
            'test_location', 'notes', 'created_at', 'updated_at',
            'created_by', 'created_by_name', 'updated_by', 'updated_by_name'
        ]
        read_only_fields = ['created_at', 'updated_at', 'created_by', 'updated_by']


class VehicleSerializer(serializers.ModelSerializer):
    """车辆序列化器"""
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    
    class Meta:
        model = Vehicle
        fields = [
            'id', 'model', 'brand', 'year', 'engine_type', 'transmission',
            'fuel_type', 'specifications', 'created_at', 'updated_at',
            'created_by', 'created_by_name'
        ]
        read_only_fields = ['created_at', 'updated_at', 'created_by']


class ComponentSerializer(serializers.ModelSerializer):
    """零部件序列化器"""
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    
    class Meta:
        model = Component
        fields = [
            'id', 'name', 'part_number', 'category', 'material', 'supplier',
            'specifications', 'description', 'created_at', 'updated_at',
            'created_by', 'created_by_name'
        ]
        read_only_fields = ['created_at', 'updated_at', 'created_by']


class ModalTestDataSerializer(serializers.ModelSerializer):
    """模态测试数据序列化器"""
    project_name = serializers.CharField(source='project.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    
    class Meta:
        model = ModalTestData
        fields = [
            'id', 'project', 'project_name', 'test_date', 'frequency_range',
            'modal_shapes', 'natural_frequencies', 'damping_ratios',
            'test_conditions', 'raw_data', 'processed_data', 'notes',
            'created_at', 'updated_at', 'created_by', 'created_by_name'
        ]
        read_only_fields = ['created_at', 'updated_at', 'created_by']


class AirtightnessTestDataSerializer(serializers.ModelSerializer):
    """气密性测试数据序列化器"""
    project_name = serializers.CharField(source='project.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    
    class Meta:
        model = AirtightnessTestData
        fields = [
            'id', 'project', 'project_name', 'test_date', 'pressure_difference',
            'air_flow_rate', 'temperature', 'humidity', 'test_images',
            'test_conditions', 'raw_data', 'processed_data', 'notes',
            'created_at', 'updated_at', 'created_by', 'created_by_name'
        ]
        read_only_fields = ['created_at', 'updated_at', 'created_by']

# Generated by Django 5.1.1 on 2025-08-24 13:52

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Component',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('name', models.CharField(max_length=255, verbose_name='零部件名称')),
                ('part_number', models.CharField(blank=True, max_length=100, verbose_name='零件号')),
                ('category', models.CharField(max_length=100, verbose_name='类别')),
                ('material', models.CharField(blank=True, max_length=100, verbose_name='材料')),
                ('supplier', models.CharField(blank=True, max_length=255, verbose_name='供应商')),
                ('specifications', models.JSONField(blank=True, default=dict, verbose_name='规格参数')),
                ('description', models.TextField(blank=True, verbose_name='描述')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='更新者')),
            ],
            options={
                'verbose_name': '零部件',
                'verbose_name_plural': '零部件',
                'db_table': 'testing_component',
            },
        ),
        migrations.CreateModel(
            name='TestProject',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('name', models.CharField(max_length=255, verbose_name='项目名称')),
                ('description', models.TextField(blank=True, verbose_name='项目描述')),
                ('project_type', models.CharField(choices=[('modal', '模态测试'), ('airtightness', '气密性测试'), ('sound_insulation', '隔音测试'), ('sound_absorption', '吸音测试'), ('sound_transmission', '传声测试'), ('wall_mounted_transmission', '壁挂传声测试'), ('material_porosity', '材料孔隙率测试')], max_length=50, verbose_name='项目类型')),
                ('status', models.CharField(choices=[('draft', '草稿'), ('active', '进行中'), ('completed', '已完成'), ('archived', '已归档')], default='draft', max_length=20, verbose_name='状态')),
                ('start_date', models.DateField(blank=True, null=True, verbose_name='开始日期')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='结束日期')),
                ('vehicle_model', models.CharField(blank=True, max_length=100, verbose_name='车型')),
                ('test_location', models.CharField(blank=True, max_length=255, verbose_name='测试地点')),
                ('notes', models.TextField(blank=True, verbose_name='备注')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='更新者')),
            ],
            options={
                'verbose_name': '测试项目',
                'verbose_name_plural': '测试项目',
                'db_table': 'testing_project',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ModalTestData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('test_date', models.DateTimeField(verbose_name='测试时间')),
                ('test_conditions', models.JSONField(blank=True, default=dict, verbose_name='测试条件')),
                ('raw_data', models.JSONField(blank=True, default=dict, verbose_name='原始数据')),
                ('processed_data', models.JSONField(blank=True, default=dict, verbose_name='处理后数据')),
                ('notes', models.TextField(blank=True, verbose_name='备注')),
                ('frequency_range', models.CharField(blank=True, max_length=100, verbose_name='频率范围')),
                ('modal_shapes', models.JSONField(blank=True, default=list, verbose_name='模态振型')),
                ('natural_frequencies', models.JSONField(blank=True, default=list, verbose_name='固有频率')),
                ('damping_ratios', models.JSONField(blank=True, default=list, verbose_name='阻尼比')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='更新者')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='testing.testproject', verbose_name='测试项目')),
            ],
            options={
                'verbose_name': '模态测试数据',
                'verbose_name_plural': '模态测试数据',
                'db_table': 'testing_modal_data',
            },
        ),
        migrations.CreateModel(
            name='AirtightnessTestData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('test_date', models.DateTimeField(verbose_name='测试时间')),
                ('test_conditions', models.JSONField(blank=True, default=dict, verbose_name='测试条件')),
                ('raw_data', models.JSONField(blank=True, default=dict, verbose_name='原始数据')),
                ('processed_data', models.JSONField(blank=True, default=dict, verbose_name='处理后数据')),
                ('notes', models.TextField(blank=True, verbose_name='备注')),
                ('pressure_difference', models.FloatField(verbose_name='压差 (Pa)')),
                ('air_flow_rate', models.FloatField(verbose_name='空气流量 (m³/h)')),
                ('temperature', models.FloatField(blank=True, null=True, verbose_name='温度 (°C)')),
                ('humidity', models.FloatField(blank=True, null=True, verbose_name='湿度 (%)')),
                ('test_images', models.JSONField(blank=True, default=list, verbose_name='测试图片')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='更新者')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='testing.testproject', verbose_name='测试项目')),
            ],
            options={
                'verbose_name': '气密性测试数据',
                'verbose_name_plural': '气密性测试数据',
                'db_table': 'testing_airtightness_data',
            },
        ),
        migrations.CreateModel(
            name='Vehicle',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('model', models.CharField(max_length=100, verbose_name='车型')),
                ('brand', models.CharField(max_length=50, verbose_name='品牌')),
                ('year', models.IntegerField(verbose_name='年份')),
                ('engine_type', models.CharField(blank=True, max_length=100, verbose_name='发动机类型')),
                ('transmission', models.CharField(blank=True, max_length=50, verbose_name='变速箱')),
                ('fuel_type', models.CharField(blank=True, max_length=50, verbose_name='燃料类型')),
                ('specifications', models.JSONField(blank=True, default=dict, verbose_name='规格参数')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='更新者')),
            ],
            options={
                'verbose_name': '车辆',
                'verbose_name_plural': '车辆',
                'db_table': 'testing_vehicle',
            },
        ),
    ]

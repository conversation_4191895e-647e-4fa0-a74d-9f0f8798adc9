from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q
from .models import TestProject, Vehicle, Component, ModalTestData, AirtightnessTestData
from .serializers import (
    TestProjectSerializer, VehicleSerializer, ComponentSerializer,
    ModalTestDataSerializer, AirtightnessTestDataSerializer
)
import logging

logger = logging.getLogger(__name__)


class TestProjectViewSet(viewsets.ModelViewSet):
    """测试项目视图集"""
    queryset = TestProject.objects.all()
    serializer_class = TestProjectSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['project_type', 'status', 'vehicle_model']
    search_fields = ['name', 'description', 'vehicle_model']
    ordering_fields = ['created_at', 'start_date', 'name']
    ordering = ['-created_at']
    
    def perform_create(self, serializer):
        """创建时设置创建者"""
        serializer.save(created_by=self.request.user)
    
    def perform_update(self, serializer):
        """更新时设置更新者"""
        serializer.save(updated_by=self.request.user)
    
    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """获取项目统计信息"""
        try:
            stats = {
                'total': self.queryset.count(),
                'by_status': {},
                'by_type': {},
                'recent': self.queryset.order_by('-created_at')[:5]
            }
            
            # 按状态统计
            for status_choice in TestProject.STATUS_CHOICES:
                status_key = status_choice[0]
                count = self.queryset.filter(status=status_key).count()
                stats['by_status'][status_key] = count
            
            # 按类型统计
            for type_choice in TestProject.PROJECT_TYPES:
                type_key = type_choice[0]
                count = self.queryset.filter(project_type=type_key).count()
                stats['by_type'][type_key] = count
            
            # 序列化最近项目
            recent_serializer = self.get_serializer(stats['recent'], many=True)
            stats['recent'] = recent_serializer.data
            
            return Response(stats)
            
        except Exception as e:
            logger.error(f"Project statistics error: {e}")
            return Response(
                {'error': 'Failed to fetch statistics'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class VehicleViewSet(viewsets.ModelViewSet):
    """车辆视图集"""
    queryset = Vehicle.objects.all()
    serializer_class = VehicleSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['brand', 'year', 'fuel_type']
    search_fields = ['model', 'brand', 'engine_type']
    ordering_fields = ['created_at', 'year', 'brand', 'model']
    ordering = ['-created_at']
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
    
    def perform_update(self, serializer):
        serializer.save(updated_by=self.request.user)


class ComponentViewSet(viewsets.ModelViewSet):
    """零部件视图集"""
    queryset = Component.objects.all()
    serializer_class = ComponentSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['category', 'material', 'supplier']
    search_fields = ['name', 'part_number', 'category', 'supplier']
    ordering_fields = ['created_at', 'name', 'category']
    ordering = ['-created_at']
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
    
    def perform_update(self, serializer):
        serializer.save(updated_by=self.request.user)


class ModalTestDataViewSet(viewsets.ModelViewSet):
    """模态测试数据视图集"""
    queryset = ModalTestData.objects.all()
    serializer_class = ModalTestDataSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['project', 'test_date']
    ordering_fields = ['created_at', 'test_date']
    ordering = ['-test_date']
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
    
    def perform_update(self, serializer):
        serializer.save(updated_by=self.request.user)


class AirtightnessTestDataViewSet(viewsets.ModelViewSet):
    """气密性测试数据视图集"""
    queryset = AirtightnessTestData.objects.all()
    serializer_class = AirtightnessTestDataSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['project', 'test_date']
    ordering_fields = ['created_at', 'test_date', 'pressure_difference']
    ordering = ['-test_date']
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
    
    def perform_update(self, serializer):
        serializer.save(updated_by=self.request.user)

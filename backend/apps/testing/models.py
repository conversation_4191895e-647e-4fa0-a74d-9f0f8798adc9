from django.db import models
from apps.core.models import BaseModel


class TestProject(BaseModel):
    """测试项目"""
    PROJECT_TYPES = [
        ('modal', '模态测试'),
        ('airtightness', '气密性测试'),
        ('sound_insulation', '隔音测试'),
        ('sound_absorption', '吸音测试'),
        ('sound_transmission', '传声测试'),
        ('wall_mounted_transmission', '壁挂传声测试'),
        ('material_porosity', '材料孔隙率测试'),
    ]
    
    STATUS_CHOICES = [
        ('draft', '草稿'),
        ('active', '进行中'),
        ('completed', '已完成'),
        ('archived', '已归档'),
    ]
    
    name = models.CharField(max_length=255, verbose_name='项目名称')
    description = models.TextField(blank=True, verbose_name='项目描述')
    project_type = models.CharField(max_length=50, choices=PROJECT_TYPES, verbose_name='项目类型')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft', verbose_name='状态')
    start_date = models.DateField(null=True, blank=True, verbose_name='开始日期')
    end_date = models.DateField(null=True, blank=True, verbose_name='结束日期')
    vehicle_model = models.CharField(max_length=100, blank=True, verbose_name='车型')
    test_location = models.CharField(max_length=255, blank=True, verbose_name='测试地点')
    notes = models.TextField(blank=True, verbose_name='备注')

    class Meta:
        db_table = 'testing_project'
        verbose_name = '测试项目'
        verbose_name_plural = '测试项目'
        ordering = ['-created_at']

    def __str__(self):
        return self.name


class Vehicle(BaseModel):
    """车辆信息"""
    model = models.CharField(max_length=100, verbose_name='车型')
    brand = models.CharField(max_length=50, verbose_name='品牌')
    year = models.IntegerField(verbose_name='年份')
    engine_type = models.CharField(max_length=100, blank=True, verbose_name='发动机类型')
    transmission = models.CharField(max_length=50, blank=True, verbose_name='变速箱')
    fuel_type = models.CharField(max_length=50, blank=True, verbose_name='燃料类型')
    specifications = models.JSONField(default=dict, blank=True, verbose_name='规格参数')

    class Meta:
        db_table = 'testing_vehicle'
        verbose_name = '车辆'
        verbose_name_plural = '车辆'

    def __str__(self):
        return f"{self.brand} {self.model} ({self.year})"


class Component(BaseModel):
    """零部件信息"""
    name = models.CharField(max_length=255, verbose_name='零部件名称')
    part_number = models.CharField(max_length=100, blank=True, verbose_name='零件号')
    category = models.CharField(max_length=100, verbose_name='类别')
    material = models.CharField(max_length=100, blank=True, verbose_name='材料')
    supplier = models.CharField(max_length=255, blank=True, verbose_name='供应商')
    specifications = models.JSONField(default=dict, blank=True, verbose_name='规格参数')
    description = models.TextField(blank=True, verbose_name='描述')

    class Meta:
        db_table = 'testing_component'
        verbose_name = '零部件'
        verbose_name_plural = '零部件'

    def __str__(self):
        return self.name


class TestData(BaseModel):
    """测试数据基类"""
    project = models.ForeignKey(TestProject, on_delete=models.CASCADE, verbose_name='测试项目')
    test_date = models.DateTimeField(verbose_name='测试时间')
    test_conditions = models.JSONField(default=dict, blank=True, verbose_name='测试条件')
    raw_data = models.JSONField(default=dict, blank=True, verbose_name='原始数据')
    processed_data = models.JSONField(default=dict, blank=True, verbose_name='处理后数据')
    notes = models.TextField(blank=True, verbose_name='备注')

    class Meta:
        abstract = True


class ModalTestData(TestData):
    """模态测试数据"""
    frequency_range = models.CharField(max_length=100, blank=True, verbose_name='频率范围')
    modal_shapes = models.JSONField(default=list, blank=True, verbose_name='模态振型')
    natural_frequencies = models.JSONField(default=list, blank=True, verbose_name='固有频率')
    damping_ratios = models.JSONField(default=list, blank=True, verbose_name='阻尼比')

    class Meta:
        db_table = 'testing_modal_data'
        verbose_name = '模态测试数据'
        verbose_name_plural = '模态测试数据'


class AirtightnessTestData(TestData):
    """气密性测试数据"""
    pressure_difference = models.FloatField(verbose_name='压差 (Pa)')
    air_flow_rate = models.FloatField(verbose_name='空气流量 (m³/h)')
    temperature = models.FloatField(blank=True, null=True, verbose_name='温度 (°C)')
    humidity = models.FloatField(blank=True, null=True, verbose_name='湿度 (%)')
    test_images = models.JSONField(default=list, blank=True, verbose_name='测试图片')

    class Meta:
        db_table = 'testing_airtightness_data'
        verbose_name = '气密性测试数据'
        verbose_name_plural = '气密性测试数据'

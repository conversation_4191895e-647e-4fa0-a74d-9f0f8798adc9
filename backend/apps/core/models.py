from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()


class BaseModel(models.Model):
    """基础模型"""
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    created_by = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='%(class)s_created',
        verbose_name='创建者'
    )
    updated_by = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='%(class)s_updated',
        verbose_name='更新者'
    )

    class Meta:
        abstract = True


class SystemConfig(BaseModel):
    """系统配置"""
    key = models.CharField(max_length=100, unique=True, verbose_name='配置键')
    value = models.TextField(verbose_name='配置值')
    description = models.CharField(max_length=255, blank=True, verbose_name='描述')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')

    class Meta:
        db_table = 'core_system_config'
        verbose_name = '系统配置'
        verbose_name_plural = '系统配置'

    def __str__(self):
        return f"{self.key}: {self.value}"


class AuditLog(BaseModel):
    """审计日志"""
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name='用户')
    action = models.CharField(max_length=100, verbose_name='操作')
    resource = models.CharField(max_length=100, verbose_name='资源')
    resource_id = models.CharField(max_length=100, blank=True, verbose_name='资源ID')
    details = models.JSONField(default=dict, blank=True, verbose_name='详细信息')
    ip_address = models.GenericIPAddressField(null=True, blank=True, verbose_name='IP地址')
    user_agent = models.TextField(blank=True, verbose_name='用户代理')

    class Meta:
        db_table = 'core_audit_log'
        verbose_name = '审计日志'
        verbose_name_plural = '审计日志'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user} - {self.action} - {self.resource}"

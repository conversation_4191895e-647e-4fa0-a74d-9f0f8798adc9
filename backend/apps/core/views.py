from django.db.models import Count
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView
from .models import SystemConfig, AuditLog
import logging

logger = logging.getLogger(__name__)


@api_view(['GET'])
@permission_classes([AllowAny])
def health_check(request):
    """系统健康检查"""
    return Response({
        'status': 'ok',
        'message': 'NVH Django API is running',
        'version': '1.0.0'
    })


class DashboardView(APIView):
    """仪表板视图"""
    
    def get(self, request):
        """获取仪表板数据"""
        try:
            # 获取统计数据
            stats = {
                'recent_activities': self.get_recent_activities(),

            }
            
            return Response(stats)
            
        except Exception as e:
            logger.error(f"Dashboard data error: {e}")
            return Response(
                {'error': 'Failed to fetch dashboard data'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def get_recent_activities(self):
        """获取最近活动"""
        try:
            recent_logs = AuditLog.objects.select_related('user').order_by('-created_at')[:10]
            activities = []
            
            for log in recent_logs:
                activities.append({
                    'id': log.id,
                    'user': log.user.username if log.user else 'System',
                    'action': log.action,
                    'resource': log.resource,
                    'timestamp': log.created_at,
                    'details': log.details
                })
            
            return activities
        except Exception as e:
            logger.error(f"Recent activities error: {e}")
            return []
    


class SystemConfigView(APIView):
    """系统配置视图"""
    
    def get(self, request):
        """获取系统配置"""
        try:
            configs = SystemConfig.objects.filter(is_active=True)
            config_data = {}
            
            for config in configs:
                config_data[config.key] = {
                    'value': config.value,
                    'description': config.description
                }
            
            return Response(config_data)
            
        except Exception as e:
            logger.error(f"System config error: {e}")
            return Response(
                {'error': 'Failed to fetch system configuration'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class UserActivityView(APIView):
    """用户活动视图"""
    
    def get(self, request):
        """获取用户活动记录"""
        try:
            user_logs = AuditLog.objects.filter(
                user=request.user
            ).order_by('-created_at')[:20]
            
            activities = []
            for log in user_logs:
                activities.append({
                    'id': log.id,
                    'action': log.action,
                    'resource': log.resource,
                    'resource_id': log.resource_id,
                    'timestamp': log.created_at,
                    'details': log.details
                })
            
            return Response(activities)
            
        except Exception as e:
            logger.error(f"User activity error: {e}")
            return Response(
                {'error': 'Failed to fetch user activities'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

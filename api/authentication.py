"""
Keycloak authentication for Django REST Framework
"""
import logging
from django.conf import settings
from rest_framework import authentication, exceptions
from rest_framework.request import Request
from keycloak import KeycloakOpenID
from django.contrib.auth.models import User
from django.contrib.auth import get_user_model

logger = logging.getLogger(__name__)

User = get_user_model()


class KeycloakAuthentication(authentication.BaseAuthentication):
    """
    Keycloak token authentication
    """

    def get_keycloak_openid(self):
        """
        Get Keycloak OpenID client instance
        """
        return KeycloakOpenID(
            server_url=settings.KEYCLOAK_SERVER_URL,
            client_id=settings.KEYCLOAK_CLIENT_ID,
            realm_name=settings.KEYCLOAK_REALM,
            client_secret_key=settings.KEYCLOAK_CLIENT_SECRET,
            verify=True
        )
    
    def authenticate(self, request: Request):
        """
        Authenticate the request and return a two-tuple of (user, token).
        """
        auth_header = request.META.get('HTTP_AUTHORIZATION')

        if not auth_header:
            return None

        try:
            # Extract token from Authorization header
            auth_parts = auth_header.split()
            if len(auth_parts) != 2 or auth_parts[0].lower() != 'bearer':
                logger.warning("Invalid authorization header format")
                return None

            token = auth_parts[1]
            logger.info(f"Attempting to validate token: {token[:20]}...")

            # Get Keycloak client
            keycloak_openid = self.get_keycloak_openid()

            # Validate token with Keycloak
            try:
                token_info = keycloak_openid.introspect(token)
                logger.info(f"Token introspection result: {token_info}")

                if not token_info.get('active', False):
                    logger.warning("Token is not active")
                    raise exceptions.AuthenticationFailed('Token is not active')

                # Get user info from token
                user_info = keycloak_openid.userinfo(token)
                logger.info(f"User info from token: {user_info}")

                # Get or create Django user
                user = self.get_or_create_user(user_info)
                logger.info(f"Authentication successful for user: {user.username}")

                return (user, token)

            except exceptions.AuthenticationFailed:
                raise
            except Exception as e:
                logger.error(f"Keycloak token validation failed: {e}")
                raise exceptions.AuthenticationFailed(f'Token validation failed: {str(e)}')

        except exceptions.AuthenticationFailed:
            raise
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            raise exceptions.AuthenticationFailed(f'Authentication error: {str(e)}')
    
    def get_or_create_user(self, user_info):
        """
        Get or create Django user from Keycloak user info
        """
        username = user_info.get('preferred_username')
        email = user_info.get('email', '')
        first_name = user_info.get('given_name', '')
        last_name = user_info.get('family_name', '')
        
        if not username:
            raise exceptions.AuthenticationFailed('Username not found in token')
        
        try:
            user = User.objects.get(username=username)
            # Update user info if needed
            if user.email != email:
                user.email = email
            if user.first_name != first_name:
                user.first_name = first_name
            if user.last_name != last_name:
                user.last_name = last_name
            user.save()
        except User.DoesNotExist:
            # Create new user
            user = User.objects.create_user(
                username=username,
                email=email,
                first_name=first_name,
                last_name=last_name
            )
        
        return user
    
    def authenticate_header(self, request):
        """
        Return a string to be used as the value of the `WWW-Authenticate`
        header in a `401 Unauthenticated` response, or `None` if the
        authentication scheme should return `403 Permission Denied` responses.
        """
        return 'Bearer'

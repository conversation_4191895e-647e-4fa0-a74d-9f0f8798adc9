"""
API Views for NVH Backend
"""
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.contrib.auth import get_user_model
from django.conf import settings

User = get_user_model()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def user_info(request):
    """
    Get current user information
    """
    user = request.user
    return Response({
        'id': user.id,
        'username': user.username,
        'email': user.email,
        'first_name': user.first_name,
        'last_name': user.last_name,
        'is_staff': user.is_staff,
        'is_superuser': user.is_superuser,
        'date_joined': user.date_joined,
        'last_login': user.last_login,
    })


@api_view(['GET'])
def health_check(request):
    """
    Health check endpoint
    """
    return Response({
        'status': 'healthy',
        'message': 'NVH Backend API is running'
    })


@api_view(['GET'])
@permission_classes([])  # 明确设置为不需要任何权限
def auth_test(request):
    """
    Authentication test endpoint - shows auth status without requiring authentication
    """
    auth_header = request.META.get('HTTP_AUTHORIZATION', 'No Authorization header')
    user_authenticated = request.user.is_authenticated
    user_info = None
    auth_error = None

    # 尝试手动进行认证测试，但不抛出异常
    if auth_header != 'No Authorization header':
        try:
            from .authentication import KeycloakAuthentication
            auth = KeycloakAuthentication()
            result = auth.authenticate(request)
            if result:
                user, token = result
                user_authenticated = True
                user_info = {
                    'id': user.id,
                    'username': user.username,
                    'email': user.email,
                }
        except Exception as e:
            auth_error = str(e)

    if user_authenticated and not user_info:
        user_info = {
            'id': request.user.id,
            'username': request.user.username,
            'email': request.user.email,
        }

    return Response({
        'auth_header': auth_header,
        'user_authenticated': user_authenticated,
        'user_info': user_info,
        'auth_error': auth_error,
        'message': 'This endpoint shows authentication status without requiring authentication'
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def dashboard_data(request):
    """
    Get dashboard data for the home page
    """
    return Response({
        'welcome_message': f'欢迎, {request.user.first_name or request.user.username}!',
        'system_info': {
            'name': 'NVH 数据管理系统',
            'version': '2.0.0',
            'description': '基于 Django + Vue 的 NVH 数据管理系统'
        },
        'user_stats': {
            'total_users': User.objects.count(),
            'current_user': request.user.username
        }
    })

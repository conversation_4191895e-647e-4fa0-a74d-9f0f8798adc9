Watching for file changes with StatReloader
"GET /api/user/info/ HTTP/1.1" 200 189
"GET /api/dashboard/ HTTP/1.1" 200 217
Not Found: /
"GET / HTTP/1.1" 404 2309
Not Found: /api/
"GET /api/ HTTP/1.1" 404 2992
Unauthorized: /api/user/info/
"GET /api/user/info/ HTTP/1.1" 401 43
D:\pythonProject\zlbbses\nvh_django\api\authentication.py changed, reloading.
Watching for file changes with StatReloader
D:\pythonProject\zlbbses\nvh_django\api\authentication.py changed, reloading.
Watching for file changes with StatReloader
D:\pythonProject\zlbbses\nvh_django\api\views.py changed, reloading.
Watching for file changes with StatReloader
D:\pythonProject\zlbbses\nvh_django\api\urls.py changed, reloading.
Watching for file changes with StatReloader
D:\pythonProject\zlbbses\nvh_django\nvh_backend\settings.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Unauthorized: /api/health/
"GET /api/health/ HTTP/1.1" 401 43
Unauthorized: /api/auth/test/
"GET /api/auth/test/ HTTP/1.1" 401 43
Unauthorized: /api/user/info/
"GET /api/user/info/ HTTP/1.1" 401 43
Attempting to validate token: invalid_token_12345...
Token introspection result: {'active': False}
Token is not active
Unauthorized: /api/user/info/
"GET /api/user/info/ HTTP/1.1" 401 32
Attempting to validate token: invalid_token_12345...
Token introspection result: {'active': False}
Token is not active
Unauthorized: /api/auth/test/
"GET /api/auth/test/ HTTP/1.1" 401 32
D:\pythonProject\zlbbses\nvh_django\nvh_backend\settings.py changed, reloading.
D:\pythonProject\zlbbses\nvh_django\nvh_backend\settings.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /api/health/ HTTP/1.1" 200 59
"GET /api/auth/test/ HTTP/1.1" 200 172
Unauthorized: /api/user/info/
"GET /api/user/info/ HTTP/1.1" 401 43
Attempting to validate token: invalid_token_12345...
Token introspection result: {'active': False}
Token is not active
Unauthorized: /api/user/info/
"GET /api/user/info/ HTTP/1.1" 401 32
Attempting to validate token: invalid_token_12345...
Token introspection result: {'active': False}
Token is not active
Unauthorized: /api/auth/test/
"GET /api/auth/test/ HTTP/1.1" 401 32
D:\pythonProject\zlbbses\nvh_django\api\views.py changed, reloading.
D:\pythonProject\zlbbses\nvh_django\api\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /api/health/ HTTP/1.1" 200 59
"GET /api/auth/test/ HTTP/1.1" 200 190
Unauthorized: /api/user/info/
"GET /api/user/info/ HTTP/1.1" 401 43
Attempting to validate token: invalid_token_12345...
Token introspection result: {'active': False}
Token is not active
Unauthorized: /api/user/info/
"GET /api/user/info/ HTTP/1.1" 401 32
Attempting to validate token: invalid_token_12345...
Token introspection result: {'active': False}
Token is not active
Unauthorized: /api/auth/test/
"GET /api/auth/test/ HTTP/1.1" 401 32
Watching for file changes with StatReloader
